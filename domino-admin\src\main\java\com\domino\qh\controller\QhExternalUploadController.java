package com.domino.qh.controller;

import cn.hutool.json.JSONUtil;
import com.domino.common.core.controller.BaseController;
import com.domino.common.core.domain.AjaxResult;
import com.domino.common.qh.dto.QhLibraryInputDTO;
import com.domino.common.qh.dto.QhPaperInputDTO;
import com.domino.common.utils.DateUtils;
import com.domino.common.utils.uuid.UUID;
import com.domino.qh.service.IQhExternalUploadService;
import com.domino.web.service.FileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/qh/external-upload")
public class QhExternalUploadController extends BaseController {

    @Resource
    private FileService fileService;

    @Autowired
    private IQhExternalUploadService qhExternalUploadService;


    /**
     * 上传文件
     */
    @GetMapping("/test")
    public AjaxResult upload(@RequestParam("uploadFile") MultipartFile uploadFile) {
        String monthFolder = DateUtils.getMonthDate();
        String uuid = UUID.randomUUID().toString().replace("-", "");
        String objectName = monthFolder + "/" + uuid + uploadFile.getOriginalFilename();
        String url = fileService.getDownloadUrl(uploadFile, objectName);
        return success(url);
    }

    /**
     * 客户端题库入库-硬编码方式
     */
    @PostMapping("/inStorage")
    public AjaxResult inStorage(@RequestBody List<QhLibraryInputDTO> inputDTOList) {
        log.info("==============inStorage入参: {}", JSONUtil.toJsonStr(inputDTOList));
        qhExternalUploadService.inStorage(inputDTOList);
        return success();
    }

    /**
     * 客户端基础数据入库-硬编码方式
     */
    @PostMapping("/inStorageByBase")
    public AjaxResult inStorageByBase(@RequestBody List<QhLibraryInputDTO> inputDTOList) {
        log.info("==============inStorageByBase入参: {}", JSONUtil.toJsonStr(inputDTOList));
        qhExternalUploadService.inStorageByBase(inputDTOList);
        return success();
    }

    /**
     * 客户端题目入库-硬编码方式
     */
    @PostMapping("/inStorageByQuestion")
    public AjaxResult inStorageByQuestion(@RequestBody List<QhLibraryInputDTO> inputDTOList) {
        log.info("==============inStorageByQuestion入参: {}", JSONUtil.toJsonStr(inputDTOList));
        qhExternalUploadService.inStorageByQuestion(inputDTOList);
        return success();
    }

    /**
     * 客户端试卷入库-硬编码方式
     */
    @PostMapping("/inStorageByPaper")
    public AjaxResult inStorageByPaper(@RequestBody List<QhLibraryInputDTO> inputDTOList) {
        log.info("==============inStorageByPaper入参: {}", JSONUtil.toJsonStr(inputDTOList));
        qhExternalUploadService.inStorageByPaper(inputDTOList);
        return success();
    }
}
