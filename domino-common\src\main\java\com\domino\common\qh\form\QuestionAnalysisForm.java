package com.domino.common.qh.form;

import lombok.Data;

import java.util.List;

@Data
public class QuestionAnalysisForm {

    /**
     * 题目图片url
     */
    private String context;

    /**
     * 解析图片url
     */
    private String questionAnalyze;

    /**
     * 答案图片url
     */
    private String questionAnswer;

    /**
     * 识图解析文本
     */
    private String ocrText;

    /**
     * 试题标签
     */
    private String tag;

    /**
     * 试题标签
     */
    private Integer score;

    /**
     * 难度
     */
    private String difficulty;

    /**
     * 类型
     */
    private String questionType;

    /**
     * 题库
     */
    private String bankId;

    /**
     * 知识点
     */
    private List<String> knowledgeTreeIds;
}
