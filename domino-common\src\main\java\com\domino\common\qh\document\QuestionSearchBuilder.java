package com.domino.common.qh.document;

import com.domino.common.qh.form.QuestionSearchForm;
import com.domino.common.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Arrays;
import java.util.List;

/**
 * 题目搜索条件构建器
 */
public class QuestionSearchBuilder {

    private QuestionSearchForm searchForm;

    private QuestionSearchBuilder() {
        this.searchForm = new QuestionSearchForm();
    }

    /**
     * 创建搜索构建器
     */
    public static QuestionSearchBuilder builder() {
        return new QuestionSearchBuilder();
    }

    /**
     * 设置关键词搜索
     */
    public QuestionSearchBuilder keyword(String keyword) {
        if (StringUtils.hasText(keyword)) {
            this.searchForm.setKeyword(keyword.trim());
        }
        return this;
    }

    /**
     * 设置题目类型（支持多选）
     */
    public QuestionSearchBuilder questionTypes(List<String> questionTypes) {
        if (!CollectionUtils.isEmpty(questionTypes)) {
            this.searchForm.setQuestionTypes(questionTypes);
        }
        return this;
    }

    /**
     * 设置题目类型（单选）
     */
    public QuestionSearchBuilder questionType(String questionType) {
        if (StringUtils.hasText(questionType)) {
            this.searchForm.setQuestionTypes(Arrays.asList(questionType));
        }
        return this;
    }

    /**
     * 设置难度（支持多选）
     */
    public QuestionSearchBuilder difficulties(List<String> difficulties) {
        if (!CollectionUtils.isEmpty(difficulties)) {
            this.searchForm.setDifficulties(difficulties);
        }
        return this;
    }

    /**
     * 设置难度（单选）
     */
    public QuestionSearchBuilder difficulty(String difficulty) {
        if (StringUtils.hasText(difficulty)) {
            this.searchForm.setDifficulties(Arrays.asList(difficulty));
        }
        return this;
    }

    /**
     * 设置试卷类型（支持多选）
     */
    public QuestionSearchBuilder paperTypes(List<String> paperTypes) {
        if (!CollectionUtils.isEmpty(paperTypes)) {
            this.searchForm.setPaperTypes(paperTypes);
        }
        return this;
    }

    /**
     * 设置试卷类型（单选）
     */
    public QuestionSearchBuilder paperType(String paperType) {
        if (StringUtils.hasText(paperType)) {
            this.searchForm.setPaperTypes(Arrays.asList(paperType));
        }
        return this;
    }

    /**
     * 设置年份（支持多选）
     */
    public QuestionSearchBuilder years(List<String> years) {
        if (!CollectionUtils.isEmpty(years)) {
            this.searchForm.setYears(years);
        }
        return this;
    }

    /**
     * 设置年份（单选）
     */
    public QuestionSearchBuilder year(String year) {
        if (StringUtils.hasText(year)) {
            this.searchForm.setYears(Arrays.asList(year));
        }
        return this;
    }

    /**
     * 设置地区（支持多选）
     */
    public QuestionSearchBuilder regions(List<String> regions) {
        if (!CollectionUtils.isEmpty(regions)) {
            this.searchForm.setRegions(regions);
        }
        return this;
    }

    /**
     * 设置地区（单选）
     */
    public QuestionSearchBuilder region(String region) {
        if (StringUtils.hasText(region)) {
            this.searchForm.setRegion(region);
        }
        return this;
    }

    /**
     * 设置试卷来源（支持多选）
     */
    public QuestionSearchBuilder paperSources(List<String> paperSources) {
        if (!CollectionUtils.isEmpty(paperSources)) {
            this.searchForm.setPaperSources(paperSources);
        }
        return this;
    }

    /**
     * 设置试卷来源（单选）
     */
    public QuestionSearchBuilder paperSource(String paperSource) {
        if (StringUtils.hasText(paperSource)) {
            this.searchForm.setPaperSources(Arrays.asList(paperSource));
        }
        return this;
    }

    /**
     * 设置试题标签（支持多选）
     */
    public QuestionSearchBuilder tags(List<String> tags) {
        if (!CollectionUtils.isEmpty(tags)) {
            this.searchForm.setTags(tags);
        }
        return this;
    }

    /**
     * 设置试题标签（单选）
     */
    public QuestionSearchBuilder tag(String tag) {
        if (StringUtils.hasText(tag)) {
            this.searchForm.setTags(Arrays.asList(tag));
        }
        return this;
    }

    /**
     * 设置知识点ID（支持多选）
     */
    public QuestionSearchBuilder knowledgeTreeIds(List<String> knowledgeTreeIds) {
        if (!CollectionUtils.isEmpty(knowledgeTreeIds)) {
            this.searchForm.setKnowledgeTreeIds(knowledgeTreeIds);
        }
        return this;
    }

    /**
     * 设置知识点关键词搜索
     */
//    public QuestionSearchBuilder knowledgeKeyword(String knowledgeKeyword) {
//        if (StringUtils.hasText(knowledgeKeyword)) {
//            this.searchForm.setKnowledgeKeyword(knowledgeKeyword.trim());
//        }
//        return this;
//    }

    /**
     * 设置年级
     */
    public QuestionSearchBuilder gradeIds(List<String> gradeIds) {
        if (!CollectionUtils.isEmpty(gradeIds)) {
            this.searchForm.setGradeIds(gradeIds);
        }
        return this;
    }

    /**
     * 设置学科
     */
    public QuestionSearchBuilder subjects(List<String> subjects) {
        if (!CollectionUtils.isEmpty(subjects)) {
            this.searchForm.setSubjects(subjects);
        }
        return this;
    }

    /**
     * 设置创建者
     */
    public QuestionSearchBuilder createBy(String createBy) {
        if (StringUtils.hasText(createBy)) {
            this.searchForm.setCreateBy(createBy);
        }
        return this;
    }

    /**
     * 设置分数范围
     */
    public QuestionSearchBuilder scoreRange(Integer minScore, Integer maxScore) {
        if (minScore != null) {
            this.searchForm.setMinScore(minScore);
        }
        if (maxScore != null) {
            this.searchForm.setMaxScore(maxScore);
        }
        return this;
    }

    /**
     * 设置分页
     */
    public QuestionSearchBuilder page(Integer pageNum, Integer pageSize) {
        if (pageNum != null && pageNum > 0) {
            this.searchForm.setPageNum(pageNum);
        }
        if (pageSize != null && pageSize > 0) {
            this.searchForm.setPageSize(pageSize);
        }
        return this;
    }

    /**
     * 设置排序
     */
    public QuestionSearchBuilder sort(String sortField, String sortOrder) {
        if (StringUtils.hasText(sortField)) {
            this.searchForm.setSortField(sortField);
        }
        if (StringUtils.hasText(sortOrder)) {
            this.searchForm.setSortOrder(sortOrder);
        }
        return this;
    }

    /**
     * 设置高亮字段
     */
    public QuestionSearchBuilder highlight(List<String> highlightFields) {
        if (!CollectionUtils.isEmpty(highlightFields)) {
            this.searchForm.setHighlightFields(highlightFields);
        }
        return this;
    }

    /**
     * 构建搜索表单
     */
    public QuestionSearchForm build() {
        // 设置默认值
        if (this.searchForm.getPageNum() == null) {
            this.searchForm.setPageNum(1);
        }
        if (this.searchForm.getPageSize() == null) {
            this.searchForm.setPageSize(10);
        }
        if (!StringUtils.hasText(this.searchForm.getSortField())) {
            this.searchForm.setSortField("createTime");
        }
        if (!StringUtils.hasText(this.searchForm.getSortOrder())) {
            this.searchForm.setSortOrder("desc");
        }
        
        return this.searchForm;
    }
}
