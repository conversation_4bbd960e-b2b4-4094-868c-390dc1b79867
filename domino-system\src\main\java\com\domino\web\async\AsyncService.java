package com.domino.web.async;

import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;

import java.util.concurrent.Future;

/**
 * 所有的异步方法都放在这管理
 */

@Slf4j
@EnableAsync
@Service
public class AsyncService {

    @Async("DominoThreadPool")
    public Future<String> test(boolean isWait) throws InterruptedException {
        return null;
    }
}
