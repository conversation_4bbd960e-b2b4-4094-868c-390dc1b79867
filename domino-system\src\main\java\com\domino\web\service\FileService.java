package com.domino.web.service;

import com.domino.common.exception.qh.QhException;
import com.domino.common.minio.MinioConfig;
import com.domino.common.minio.MinioUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.InputStream;
import java.util.List;

/**
 * 文件存储service
 *
 */
@Service
public class FileService {

    @Resource
    private MinioConfig minioConfig;



    /**
     *
     * @param inputStream inputStream
     * @param objectName objectName
     * @param contentType contentType
     * @throws Exception 上传文件 -流方式 uploadInputStreamMinIO
     */
    public void uploadInputStreamMinIO(InputStream inputStream, String objectName, String contentType) throws Exception {
        // 检查存储桶是否存在
        boolean bucketExists = MinioUtil.bucketExists(minioConfig.getBucketName());
        if (!bucketExists) {
            //MinioUtil.createBucket(minioConfig.getBucketName());
        }
        if (StringUtils.isNotBlank(objectName)) {
            MinioUtil.uploadFile(minioConfig.getBucketName(), objectName, inputStream);
        } else {
            MinioUtil.uploadFile(minioConfig.getBucketName(), objectName, inputStream);
        }
    }

    /**
     * 获取图片url链接 -流方式
     * @param inputStream inputStream
     * @param objectName objectName
     * @param contentType contentType
     * @return String
     */
    public String getDownloadUrlByInputStream(InputStream inputStream, String objectName, String contentType) {
        String fileUrl = "";
        try {
            uploadInputStreamMinIO(inputStream, objectName, contentType);
            fileUrl = MinioUtil.getPresignedObjectUrl(minioConfig.getBucketName(), objectName);
        } catch (Exception e) {
            throw new QhException("MinIO上传获取图片链接失败,请检查！", e.getMessage());
        }
        return fileUrl;
    }

    /**
     *
     * @param uploadFile uploadFile
     * @param objectName objectName
     * @throws Exception 上传文件
     */
    public void uploadToMinIO(MultipartFile uploadFile, String objectName) throws Exception {
        //bucket创建待定
        if (StringUtils.isNotBlank(objectName)) {
            MinioUtil.uploadFile(minioConfig.getBucketName(), objectName, uploadFile.getInputStream());
        } else {
            MinioUtil.uploadFile(minioConfig.getBucketName(), uploadFile.getOriginalFilename(), uploadFile.getInputStream());
        }
    }

    /**
     *
     * @param uploadFile uploadFile
     * @param objectName objectName
     * @return 获取图片url链接
     */
    public String getDownloadUrl(MultipartFile uploadFile, String objectName) {
        String fileUrl = "";
        try {
            uploadToMinIO(uploadFile, objectName);
            fileUrl = MinioUtil.getPresignedObjectUrl(minioConfig.getBucketName(), objectName);
        } catch (Exception e) {
            throw new QhException("MinIO上传获取图片链接失败,请检查！", e.getMessage());
        }
        return fileUrl;
    }

    public void removeFiles(List<String> objectNames) {
        MinioUtil.removeFiles(minioConfig.getBucketName(), objectNames);
    }
}

