package com.domino.common.qh.domain;

import com.domino.common.annotation.Excel;
import com.domino.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 学科对象 qh_discipline
 *
 * <AUTHOR>
 * @date 2025-04-06
 */
@Data
public class QhDiscipline extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private String id;

    /**
     * 年级ID
     */
    private String gradeId;

    /**
     * 年级ID
     */
    @Excel(name = "年级")
    private String gradeName;

    /**
     * 学科名称
     */
    @Excel(name = "学科名称")
    private String disciplineName;

    /**
     * 创建人
     */
    @Excel(name = "创建人")
    private String createdBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdTime;

    /**
     * 更新人
     */
    @Excel(name = "更新人")
    private String updatedBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updatedTime;

    /**
     * 逻辑删除
     */
    private Long delFlag;
}
