// package com.domino.qh.service.impl;
//
// import com.domino.qh.service.OcrService;
// import net.sourceforge.tess4j.Tesseract;
// import net.sourceforge.tess4j.TesseractException;
// import net.sourceforge.tess4j.util.ImageHelper;
// import org.apache.commons.lang3.StringUtils;
// import org.apache.http.HttpEntity;
// import org.apache.http.client.methods.CloseableHttpResponse;
// import org.apache.http.client.methods.HttpGet;
// import org.apache.http.impl.client.CloseableHttpClient;
// import org.apache.http.impl.client.HttpClients;
// import org.springframework.beans.factory.annotation.Value;
// import org.springframework.stereotype.Service;
//
// import javax.imageio.ImageIO;
// import java.awt.image.BufferedImage;
// import java.io.IOException;
// import java.io.InputStream;
//
// import static net.sourceforge.tess4j.ITessAPI.TessOcrEngineMode.OEM_LSTM_ONLY;
//
// /**
//  * 使用Tesseract做OCR识别
//  */
//
// @Service
// public class OcrTesseractServiceImpl implements OcrService {
//
//     @Value("${ocr.tesseract.datapath}")
//     private String dataPath;
//
//     public String recognizeText(String url) throws Exception {
//         Tesseract tesseract = new Tesseract();
//         tesseract.setDatapath(dataPath); // 设置语言包路径
//         tesseract.setLanguage("chi_sim+eng"); // 中文和英文
//         tesseract.setOcrEngineMode(OEM_LSTM_ONLY);  // 设置为 LSTM 引擎
//         BufferedImage image = downloadImage(url);
//         // 转换为灰度图
//         BufferedImage grayImage = ImageHelper.convertImageToGrayscale(image);
//         // 提高对比度
//         BufferedImage highContrastImage = ImageHelper.getScaledInstance(grayImage, grayImage.getWidth() * 2, grayImage.getHeight() * 2);
//         // OCR识别内容
//         return StringUtils.deleteWhitespace(tesseract.doOCR(highContrastImage));
//     }
//
//     public static BufferedImage downloadImage(String url) throws Exception {
//         CloseableHttpClient httpClient = HttpClients.createDefault();
//         HttpGet httpGet = new HttpGet(url);
//
//         try (CloseableHttpResponse response = httpClient.execute(httpGet)) {
//             HttpEntity entity = response.getEntity();
//             if (entity == null) {
//                 throw new RuntimeException("无法获取图片内容");
//             }
//             try (InputStream inputStream = entity.getContent()) {
//                 return ImageIO.read(inputStream);
//             }
//         }
//     }
//
//     @Override
//     public String recognizeText(InputStream inputStream) throws IOException, TesseractException {
//         Tesseract tesseract = new Tesseract();
//         tesseract.setDatapath(dataPath); // 设置语言包路径
//         tesseract.setLanguage("chi_sim+eng"); // 中文和英文
//         tesseract.setOcrEngineMode(OEM_LSTM_ONLY);  // 设置为 LSTM 引擎
//         BufferedImage image = ImageIO.read(inputStream);
//         // 转换为灰度图
//         BufferedImage grayImage = ImageHelper.convertImageToGrayscale(image);
//         // 提高对比度
//         BufferedImage highContrastImage = ImageHelper.getScaledInstance(grayImage, grayImage.getWidth() * 2, grayImage.getHeight() * 2);
//         // OCR识别内容
//         return StringUtils.deleteWhitespace(tesseract.doOCR(highContrastImage));
//     }
// }
