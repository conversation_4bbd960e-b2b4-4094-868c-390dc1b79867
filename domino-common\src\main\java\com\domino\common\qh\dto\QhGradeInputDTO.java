package com.domino.common.qh.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * 年级信息
 *
 */

@Data
public class QhGradeInputDTO implements Serializable {
    private static final long serialVersionUID = 1276242310443212893L;

    private String gradeId;

    @NotBlank(message = "年级名称不能为空")
    private String gradeName;

    private List<QhChapterInputDTO> chapterInputDTOList;

}
