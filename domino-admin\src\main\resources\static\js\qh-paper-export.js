/**
 * 试卷导出功能前端API调用示例（同步版本）
 *
 * <AUTHOR>
 * @date 2025-08-11
 */

// 导出试卷为PDF（同步）
function exportPaperToPdf(paperId, options) {
    const exportData = {
        paperId: paperId,
        exportType: 'PDF',
        contentOptions: {
            includeQuestions: options.includeQuestions !== false,
            includeAnswers: options.includeAnswers || false,
            includeAnalysis: options.includeAnalysis || false
        },
        formatOptions: {
            pageSize: options.pageSize || 'A4',
            fontSize: options.fontSize || 12,
            showQuestionNumbers: options.showQuestionNumbers !== false,
            showScores: options.showScores !== false,
            margin: options.margin || 20,
            lineSpacing: options.lineSpacing || 1.5
        },
        flag: options.flag || 'ZJ'
    };

    // 使用fetch进行同步下载
    downloadFile('/qh/paper/export/pdf', exportData);
}

// 导出试卷为Word（同步）
function exportPaperToWord(paperId, options) {
    const exportData = {
        paperId: paperId,
        exportType: 'WORD',
        contentOptions: {
            includeQuestions: options.includeQuestions !== false,
            includeAnswers: options.includeAnswers || false,
            includeAnalysis: options.includeAnalysis || false
        },
        formatOptions: {
            pageSize: options.pageSize || 'A4',
            fontSize: options.fontSize || 12,
            showQuestionNumbers: options.showQuestionNumbers !== false,
            showScores: options.showScores !== false,
            margin: options.margin || 20,
            lineSpacing: options.lineSpacing || 1.5
        },
        flag: options.flag || 'ZJ'
    };

    // 使用fetch进行同步下载
    downloadFile('/qh/paper/export/word', exportData);
}

// 通用文件下载函数
async function downloadFile(url, data) {
    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });

        if (response.ok) {
            // 获取文件名
            const contentDisposition = response.headers.get('Content-Disposition');
            let filename = 'export.' + (data.exportType === 'PDF' ? 'pdf' : 'docx');
            if (contentDisposition) {
                const matches = contentDisposition.match(/filename=(.+)/);
                if (matches) {
                    filename = decodeURIComponent(matches[1]);
                }
            }

            // 下载文件
            const blob = await response.blob();
            const downloadUrl = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = downloadUrl;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(downloadUrl);

            console.log('导出成功');
        } else {
            const errorText = await response.text();
            throw new Error('导出失败: ' + errorText);
        }
    } catch (error) {
        console.error('下载失败:', error);
        alert('导出失败: ' + error.message);
    }
}

// 使用示例
/*
// 1. 导出PDF（仅试卷）
exportPaperToPdf('paper123', {
    includeQuestions: true,
    includeAnswers: false,
    includeAnalysis: false
});

// 2. 导出Word（试卷+答案+解析）
exportPaperToWord('paper123', {
    includeQuestions: true,
    includeAnswers: true,
    includeAnalysis: true,
    fontSize: 14,
    pageSize: 'A4'
});

// 3. 导出PDF（试卷+答案）
exportPaperToPdf('paper123', {
    includeQuestions: true,
    includeAnswers: true,
    includeAnalysis: false,
    fontSize: 12,
    showScores: true,
    flag: 'ZJ'
});
*/
