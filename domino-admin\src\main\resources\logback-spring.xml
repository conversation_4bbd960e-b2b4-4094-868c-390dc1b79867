<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="30 seconds">
    <!-- 彩色日志依赖 -->
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter" />
    <conversionRule conversionWord="wex" converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter" />
    <conversionRule conversionWord="wEx" converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter" />

    <!-- 日志文件存储路径 -->
    <property name="LOG_PATH" value="./logs"/>

    <!-- 控制台日志格式 -->
    <property name="CONSOLE_LOG_PATTERN"
              value="${CONSOLE_LOG_PATTERN:-%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%5p) %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n%wEx}" />

    <!-- 文件日志格式 -->
    <property name="FILE_LOG_PATTERN"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS} %5p --- [%15.15t] %-40.40logger{39} : %m%n%wEx" />

    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 滚动文件输出 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/domino-admin.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/domino-admin.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- ============== FLINK CDC 日志优化 ============== -->
    <!-- 1. 核心CDC包降级到WARN -->
    <logger name="com.ververica" level="WARN" additivity="false">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
    </logger>

    <!-- 2. 特别处理表发现日志 -->
    <logger name="com.ververica.cdc.connectors.mysql.source.utils.TableDiscoveryUtils" level="ERROR" additivity="false">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
    </logger>

    <!-- 3. Flink相关日志控制 -->
    <logger name="org.apache.flink" level="INFO" />
    <logger name="org.apache.kafka" level="WARN" />
    <logger name="org.apache.curator" level="ERROR" />

    <!-- ============== 应用日志配置 ============== -->
    <springProfile name="dev,test">
        <!-- 开发测试环境详细日志 -->
        <logger name="org.springframework" level="INFO" />
        <logger name="com.netflix" level="INFO" />
        <logger name="java.sql" level="DEBUG" />
        <logger name="com.pro" level="DEBUG" />

        <root level="INFO">
            <appender-ref ref="CONSOLE" />
            <appender-ref ref="FILE" />
        </root>
    </springProfile>

    <springProfile name="prod">
        <!-- 生产环境精简日志 -->
        <logger name="org.springframework" level="WARN" />
        <logger name="com.netflix" level="WARN" />
        <logger name="java.sql" level="WARN" />
        <logger name="com.pro" level="INFO" />

        <root level="WARN">
            <appender-ref ref="FILE" />
            <!-- 生产环境可关闭控制台日志 -->
            <!-- <appender-ref ref="CONSOLE" /> -->
        </root>
    </springProfile>

    <!-- 默认配置（无环境指定时） -->
    <springProfile name="!dev,!test,!prod">
        <logger name="org.springframework" level="INFO" />
        <logger name="com.netflix" level="INFO" />
        <logger name="java.sql" level="INFO" />
        <logger name="com.pro" level="INFO" />

        <root level="INFO">
            <appender-ref ref="CONSOLE" />
            <appender-ref ref="FILE" />
        </root>
    </springProfile>
</configuration>
