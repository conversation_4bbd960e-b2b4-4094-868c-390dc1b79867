package com.domino.common.qh.dto;

import com.domino.common.qh.domain.QhQuestionBank;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

@Data
@AllArgsConstructor
public class PaperChromosome {
    private List<QhQuestionBank> questions; // 题目列表
    private double fitness;                 // 适应度得分
    private double difficultyScore;         // 难度得分
    private double coverageScore;           // 知识点覆盖得分
    private double typeMatchScore;          // 新增：题型匹配得分
}