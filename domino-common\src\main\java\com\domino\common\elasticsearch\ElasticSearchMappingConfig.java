package com.domino.common.elasticsearch;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * Elasticsearch映射配置类
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "elasticsearch.mappings")
public class ElasticSearchMappingConfig {

    /**
     * 题目索引配置, 分片副本视情况增加，默认11
     */
    private QuestionMappingConfig question = new QuestionMappingConfig();

    @Data
    public static class QuestionMappingConfig {
        /**
         * 索引名称
         */
        private String indexName = "qh_questions";
        
        /**
         * 分片数
         */
        private Integer numberOfShards = 1;
        
        /**
         * 副本数
         */
        private Integer numberOfReplicas = 1;
        
        /**
         * 字段映射配置
         */
        private Map<String, FieldMapping> properties = new HashMap<>();
        
        /**
         * 分析器配置
         */
        private Map<String, AnalyzerConfig> analyzers = new HashMap<>();
        
        /**
         * 是否启用映射配置
         */
        private Boolean enabled = true;
        
        /**
         * 生成完整的映射JSON配置
         */
        public String toMappingJson() {
            Map<String, Object> mapping = new HashMap<>();

            // mappings部分
            Map<String, Object> mappings = new HashMap<>();
            mappings.put("properties", convertPropertiesToESFormat(properties));
            mapping.put("mappings", mappings);

            // settings部分
            Map<String, Object> settings = new HashMap<>();
            settings.put("number_of_shards", numberOfShards);
            settings.put("number_of_replicas", numberOfReplicas);

            if (!analyzers.isEmpty()) {
                Map<String, Object> analysis = new HashMap<>();
                Map<String, Object> analyzerMap = new HashMap<>();
                analyzers.forEach((key, value) -> analyzerMap.put(key, value.toMap()));
                analysis.put("analyzer", analyzerMap);
                settings.put("analysis", analysis);
            }

            mapping.put("settings", settings);

            return JSON.toJSONString(mapping);
        }

        /**
         * 将Java驼峰命名的属性转换为ES下划线命名格式
         */
        private Map<String, Object> convertPropertiesToESFormat(Map<String, FieldMapping> properties) {
            Map<String, Object> esProperties = new HashMap<>();

            properties.forEach((fieldName, fieldMapping) -> {
                Map<String, Object> esFieldMapping = convertFieldMappingToESFormat(fieldMapping);
                esProperties.put(fieldName, esFieldMapping);
            });

            return esProperties;
        }

        /**
         * 将FieldMapping转换为ES格式的Map
         */
        private Map<String, Object> convertFieldMappingToESFormat(FieldMapping fieldMapping) {
            Map<String, Object> esMapping = new HashMap<>();

            if (fieldMapping.getType() != null) {
                esMapping.put("type", fieldMapping.getType());
            }
            if (fieldMapping.getAnalyzer() != null) {
                esMapping.put("analyzer", fieldMapping.getAnalyzer());
            }
            if (fieldMapping.getSearchAnalyzer() != null) {
                esMapping.put("search_analyzer", fieldMapping.getSearchAnalyzer());
            }
            if (fieldMapping.getFormat() != null) {
                esMapping.put("format", fieldMapping.getFormat());
            }
            if (fieldMapping.getIgnoreAbove() != null) {
                esMapping.put("ignore_above", fieldMapping.getIgnoreAbove()); // 关键修复：使用下划线
            }
            if (fieldMapping.getFields() != null && !fieldMapping.getFields().isEmpty()) {
                esMapping.put("fields", convertPropertiesToESFormat(fieldMapping.getFields()));
            }

            return esMapping;
        }
    }

    @Data
    public static class FieldMapping {
        /**
         * 字段类型
         */
        private String type;

        /**
         * 分析器
         */
        private String analyzer;

        /**
         * 搜索分析器
         */
        private String searchAnalyzer;

        /**
         * 日期格式
         */
        private String format;

        /**
         * 是否忽略超长字段
         */
        private Integer ignoreAbove;

        /**
         * 多字段映射
         */
        private Map<String, FieldMapping> fields;
    }
    
    @Data
    public static class AnalyzerConfig {
        /**
         * 分析器类型
         */
        private String type;
        
        /**
         * 分词器
         */
        private String tokenizer;
        
        /**
         * 过滤器
         */
        private String[] filters;
        
        /**
         * 转换为Map格式
         */
        public Map<String, Object> toMap() {
            Map<String, Object> map = new HashMap<>();
            if (type != null) {
                map.put("type", type);
            }
            if (tokenizer != null) {
                map.put("tokenizer", tokenizer);
            }
            if (filters != null && filters.length > 0) {
                map.put("filters", filters);
            }
            return map;
        }
    }
}
