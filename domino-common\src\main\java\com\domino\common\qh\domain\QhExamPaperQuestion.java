package com.domino.common.qh.domain;

import com.domino.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.domino.common.annotation.Excel;

import java.io.Serializable;

/**
 * 试卷分题目关联对象 qh_exam_paper_question
 *
 * <AUTHOR>
 * @date 2025-04-21
 */
public class QhExamPaperQuestion implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private String id;

    /** 试卷ID */
    @Excel(name = "试卷ID")
    private String paperId;

    /** 题目ID */
    @Excel(name = "题目ID")
    private String questionBankId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPaperId() {
        return paperId;
    }

    public void setPaperId(String paperId) {
        this.paperId = paperId;
    }

    public String getQuestionBankId() {
        return questionBankId;
    }

    public void setQuestionBankId(String questionBankId) {
        this.questionBankId = questionBankId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("paperId", getPaperId())
            .append("questionBankId", getQuestionBankId())
            .toString();
    }
}
