package com.domino.qh.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.domino.common.exception.qh.QhException;
import com.domino.common.qh.domain.QhKnowledgeQuestion;
import com.domino.common.qh.domain.QhKnowledgeTree;
import com.domino.common.qh.domain.QhQuestionBank;
import com.domino.common.qh.dto.*;
import com.domino.common.utils.DateUtils;
import com.domino.common.utils.SecurityUtils;
import com.domino.common.utils.uuid.IdUtils;
import com.domino.qh.mapper.QhKnowledgeQuestionMapper;
import com.domino.qh.mapper.QhKnowledgeTreeMapper;
import com.domino.qh.mapper.QhQuestionBankMapper;
import com.domino.qh.service.IQhExternalUploadService;
import com.domino.web.service.FileService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service
public class QhExternalUploadServiceImpl implements IQhExternalUploadService {

    public static final String Context = "context";
    public static final String ANALYZE = "analyze";

    @Resource
    private FileService fileService;

    @Autowired
    private QhKnowledgeTreeMapper knowledgeTreeMapper;

    @Autowired
    private QhQuestionBankMapper qhQuestionBankMapper;

    @Autowired
    private QhKnowledgeQuestionMapper qhKnowledgeQuestionMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void inStorageByBase(List<QhLibraryInputDTO> inputDTOList) {
        long start = System.currentTimeMillis();
        log.info("=========客户端题库基础数据入库inStorageByBase开始时间{}================", + start);
        // 校验基础信息
        checkData(inputDTOList, Boolean.TRUE);  //调试再改动
        // 入库题库、年级，章节、知识点信息
        try {
            //记录操作日志, 用切面记录
            for (QhLibraryInputDTO inputDTO : inputDTOList) {
                List<QhKnowledgeTree> knowledgeTreeList = new ArrayList<>();
                //组装基础数据信息
                bulidBaseData(inputDTO, knowledgeTreeList);
                //批量新增数据
                log.info("=========客户端题库基础数据入库批量新增基础数据开始================");
                //knowledgeTreeMapper.batchInsert(knowledgeTreeList);
                for (QhKnowledgeTree qhKnowledgeTree : knowledgeTreeList) {
                    knowledgeTreeMapper.insertKnowledge(qhKnowledgeTree);
                }
                log.info("=========客户端题库基础数据入库批量新增结束================");
            }
        } catch (Exception e) {
            throw new QhException("上传题库基础数据失败，请联系管理员！", e.getMessage());
        }
        long end = System.currentTimeMillis();
        log.info("=========客户端题库基础数据入库inStorageByBase总耗时{}================", end - start);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void inStorageByQuestion(List<QhLibraryInputDTO> inputDTOList) {
        long start = System.currentTimeMillis();
        log.info("=========客户端题目入库inStorageByQuestion开始时间{}================", + start);
        // 校验基础信息
        //checkData(inputDTOList, Boolean.TRUE);  //调试再改动
        // 入库题目
        List<UploadToMinIOResult> result = new ArrayList<>();
        try {
            //记录操作日志, 用切面记录
            for (QhLibraryInputDTO inputDTO : inputDTOList) {
                List<QhQuestionBank> questionBankList = new ArrayList<>();
                // 组装题目表
                batchBulidQuestionBank(inputDTO.getQuestionInputDTOList(),
                        inputDTO.getKnowledgeQuestionDTOList(), inputDTO, questionBankList);
                for (QhQuestionBank qhQuestionBank : questionBankList) {
                    //组装图片链接
                    bulidQuestionUrl(qhQuestionBank, result);
                }
                //批量新增数据
                log.info("=========客户端题目入库批量新增题目开始================");
                //qhQuestionBankMapper.batchInsert(questionBankList);
                for (QhQuestionBank qhQuestionBank : questionBankList) {
                    qhQuestionBankMapper.insertQuestionBank(qhQuestionBank);
                }
                log.info("=========客户端题目入库批量新增题目结束================");
            }
        } catch (Exception e) {
            if (CollectionUtils.isNotEmpty(result)) {
                List<String> objectNameList = result.stream().map(UploadToMinIOResult::getObjectName).collect(Collectors.toList());
                //获取url，根据url获取objectName，调用removeObject移除图片
                fileService.removeFiles(objectNameList);
            }
            throw new QhException("上传题目失败，请联系管理员！", e.getMessage());
        }
        long end = System.currentTimeMillis();
        log.info("=========客户端题目入库inStorageByQuestion总耗时{}================", end - start);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void inStorage(List<QhLibraryInputDTO> inputDTOList) {
        long start = System.currentTimeMillis();
        log.info("=========客户端题库入库inStorage开始时间{}================", + start);
        // 校验基础信息
        checkData(inputDTOList, Boolean.TRUE);  //调试再改动
        //入库，先入库题库、年级，章节、知识点信息，最后入库题目
        List<UploadToMinIOResult> result = new ArrayList<>();
        try {
            //记录操作日志, 用切面记录
            for (QhLibraryInputDTO inputDTO : inputDTOList) {
                List<QhKnowledgeTree> knowledgeTreeList = new ArrayList<>();
                List<QhQuestionBank> questionBankList = new ArrayList<>();
                //组装基础数据信息
                bulidBaseData(inputDTO, knowledgeTreeList);
                // 组装题目表
                batchBulidQuestionBank(inputDTO.getQuestionInputDTOList(),
                        inputDTO.getKnowledgeQuestionDTOList(), inputDTO, questionBankList);
                for (QhQuestionBank qhQuestionBank : questionBankList) {
                    //组装图片链接
                    bulidQuestionUrl(qhQuestionBank, result);
                }
                //批量新增数据
                log.info("=========客户端题库入库批量新增基础数据开始================");
                //knowledgeTreeMapper.batchInsert(knowledgeTreeList);
                for (QhKnowledgeTree qhKnowledgeTree : knowledgeTreeList) {
                    knowledgeTreeMapper.insertKnowledge(qhKnowledgeTree);
                }
                log.info("=========客户端题库入库批量新增基础数据结束================");
                log.info("=========客户端题库入库批量新增题目开始================");
                //qhQuestionBankMapper.batchInsert(questionBankList);
                for (QhQuestionBank qhQuestionBank : questionBankList) {
                    qhQuestionBankMapper.insertQuestionBank(qhQuestionBank);
                }
                log.info("=========客户端题库入库批量新增题目结束================");
            }
        } catch (Exception e) {
            if (CollectionUtils.isNotEmpty(result)) {
                List<String> objectNameList = result.stream().map(UploadToMinIOResult::getObjectName).collect(Collectors.toList());
                //获取url，根据url获取objectName，调用removeObject移除图片
                fileService.removeFiles(objectNameList);
            }
            throw new QhException("上传题库失败，请联系管理员！", e.getMessage());
        }
        long end = System.currentTimeMillis();
        log.info("=========客户端题库入库inStorage总耗时{}================", end - start);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void inStorageByPaper(List<QhLibraryInputDTO> inputDTOList) {
        long start = System.currentTimeMillis();
        log.info("=========客户端试卷入库inStorageByPaper开始时间{}================", + start);
        // 校验基础信息
        //checkData(inputDTOList, Boolean.TRUE);  //调试再改动
        // 入库试卷
        try {
            //记录操作日志, 用切面记录
            for (QhLibraryInputDTO inputDTO : inputDTOList) {
                List<QhPaperInputDTO> paperInputList = new ArrayList<>();
                //组装试卷信息
                bulidPaperInfo(inputDTO, paperInputList);
                //批量新增数据
                log.info("=========客户端试卷入库批量新增开始================");
                //paperMapper.batchInsert(knowledgeTreeList);
                for (QhPaperInputDTO paperInputDTO : paperInputList) {
                    //paperMapper.insert(paperInputDTO);
                }
                log.info("=========客户端试卷入库批量新增结束================");
            }
        } catch (Exception e) {
            throw new QhException("上传试卷失败，请联系管理员！", e.getMessage());
        }
        long end = System.currentTimeMillis();
        log.info("=========客户端试卷入库inStorageByPaper总耗时{}================", end - start);

    }

    public void bulidPaperInfo(QhLibraryInputDTO inputDTO, List<QhPaperInputDTO> paperInputList) {
        //TODO
    }

    public void bulidQuestionUrl(QhQuestionBank qhQuestionBank, List<UploadToMinIOResult> result) {
        //存入minio
        //String contentType = "image/png";
        InputStream contextInputStream = parseBase64ToInputStream(qhQuestionBank.getContext()); //题目图片
        InputStream analyzeInStream = parseBase64ToInputStream(qhQuestionBank.getQuestionAnalyze()); //解析图片
        String monthFolder = DateUtils.getMonthDate();
        String uuid = IdUtils.simpleUUID();
        //Context
        String objectNameContext = monthFolder + "/" + uuid + Context;  //考虑拼接题目名称
        String contextUrl = fileService.getDownloadUrlByInputStream(contextInputStream, objectNameContext, null);
        qhQuestionBank.setContext(contextUrl);
        result.add(new UploadToMinIOResult(qhQuestionBank.getContext(), objectNameContext, true));
        //ANALYZE
        String objectNameAnalyze = monthFolder + "/" + uuid + ANALYZE;
        String analyzeUrl = fileService.getDownloadUrlByInputStream(analyzeInStream, objectNameAnalyze, null);
        qhQuestionBank.setQuestionAnalyze(analyzeUrl);
        result.add(new UploadToMinIOResult(qhQuestionBank.getQuestionAnalyze(), objectNameAnalyze, true));
    }

    public void batchBulidQuestionBank(List<QhQuestionInputDTO> questionInputDTOList,
                                          List<QhKnowledgeQuestionDTO> knowledgeQuestionMappings,
                                          QhLibraryInputDTO libraryInputDTO,
                                          List<QhQuestionBank> questionBankList) {
        // 遍历每个题目进行组装
        for (QhQuestionInputDTO questionDTO : questionInputDTOList) {
            QhQuestionBank questionBank = bulidQuestionBank(questionDTO,
                    knowledgeQuestionMappings, libraryInputDTO);
            questionBankList.add(questionBank);
        }
    }

    // 构建章节ID到章节对象的映射 支持子章节递归
    private Map<String, QhChapterInputDTO> buildChapterMap(QhLibraryInputDTO libraryInputDTO) {
        Map<String, QhChapterInputDTO> chapterMap = new HashMap<>();
        List<QhChapterInputDTO> chapters = libraryInputDTO.getGradeInputDTO().getChapterInputDTOList();
        flattenChapters(chapters, chapterMap);
        return chapterMap;
    }

    private void flattenChapters(List<QhChapterInputDTO> chapters, Map<String, QhChapterInputDTO> map) {
        for (QhChapterInputDTO chapter : chapters) {
            map.put(chapter.getChapterId(), chapter);
            if (CollectionUtils.isNotEmpty(chapter.getChildrenList())) {
                flattenChapters(chapter.getChildrenList(), map);
            }
        }
    }

    // 构建知识点ID到知识点对象的映射 支持子知识点递归
    private Map<String, QhKnowledgeInputDTO> buildKnowledgeMap(List<QhKnowledgeInputDTO> knowledgeList) {
        Map<String, QhKnowledgeInputDTO> knowledgeMap = new HashMap<>();
        flattenKnowledge(knowledgeList, knowledgeMap);
        return knowledgeMap;
    }

    private void flattenKnowledge(List<QhKnowledgeInputDTO> knowledgeList, Map<String, QhKnowledgeInputDTO> map) {
        for (QhKnowledgeInputDTO knowledge : knowledgeList) {
            map.put(knowledge.getKnowledgePointId(), knowledge);
            if (CollectionUtils.isNotEmpty(knowledge.getChildrenList())) {
                flattenKnowledge(knowledge.getChildrenList(), map);
            }
        }
    }

    private QhQuestionBank bulidQuestionBank(QhQuestionInputDTO questionInputDTO,
                                                List<QhKnowledgeQuestionDTO> knowledgeQuestionMappings,
                                                QhLibraryInputDTO libraryInputDTO) {
        QhQuestionBank questionBank = new QhQuestionBank();

        // 基础字段映射
        questionBank.setId(questionInputDTO.getQuestionId());
        questionBank.setContext(StrUtil.isBlank(questionInputDTO.getQuestionImage()) ? questionInputDTO.getQuestionDocument() : questionInputDTO.getQuestionImage());
        questionBank.setQuestionAnalyze(StrUtil.isBlank(questionInputDTO.getAnswerImage()) ? questionInputDTO.getAnswerDocument() : questionInputDTO.getAnswerImage());
        questionBank.setQuestionType("1");  //questionInputDTO.getQuestionType()
        questionBank.setScore(questionInputDTO.getScore());
        questionBank.setDifficulty("1");  //questionInputDTO.getDifficultyLevel()
        questionBank.setDelFlag("0");
        questionBank.setStatus("0");

        // 获取当前题目关联的知识点ID列表,若客户端knowledgePointIDs字段传了，这个步骤和题目知识点关联可以不传
        List<String> knowledgePointIds;
        if (CollectionUtils.isNotEmpty(questionInputDTO.getKnowledgePointIDs())) {
            knowledgePointIds = new ArrayList<>(questionInputDTO.getKnowledgePointIDs());
            // questionBank.setKnowledgeTreeIds(String.join(",", knowledgePointIds));
            knowledgePointIds.forEach(knowledgeTreeId -> {
                QhKnowledgeQuestion qhKnowledgeQuestion = new QhKnowledgeQuestion();
                qhKnowledgeQuestion.setId(IdUtil.fastSimpleUUID());
                qhKnowledgeQuestion.setKnowledgeTreeId(knowledgeTreeId);
                qhKnowledgeQuestion.setQuestionBankId(questionBank.getId());
                qhKnowledgeQuestion.setCreateBy(SecurityUtils.getUsername());
                qhKnowledgeQuestionMapper.insertKnowledgeQuestion(qhKnowledgeQuestion);
            });
        } else {
            knowledgePointIds = knowledgeQuestionMappings.stream()
                    .filter(mapping -> mapping.getQuestionId().equals(questionInputDTO.getQuestionId()))
                    .map(QhKnowledgeQuestionDTO::getKnowledgePointId)
                    .distinct().collect(Collectors.toList());
            // questionBank.setKnowledgeTreeIds(String.join(",", knowledgePointIds));
            knowledgePointIds.forEach(knowledgeTreeId -> {
                QhKnowledgeQuestion qhKnowledgeQuestion = new QhKnowledgeQuestion();
                qhKnowledgeQuestion.setId(IdUtil.fastSimpleUUID());
                qhKnowledgeQuestion.setKnowledgeTreeId(knowledgeTreeId);
                qhKnowledgeQuestion.setQuestionBankId(questionBank.getId());
                qhKnowledgeQuestion.setCreateBy(SecurityUtils.getUsername());
                qhKnowledgeQuestionMapper.insertKnowledgeQuestion(qhKnowledgeQuestion);
            });
        }

        // 组装知识点树数据及层级信息, 可有可无, 目前先注释掉
        //List<QhKnowledgeTree> knowledgeTreeNodes = new ArrayList<>();
        //buildKnowledgeTree(knowledgePointIds, libraryInputDTO, knowledgeTreeNodes);

        // 填充知识点列表及附加信息
        //questionBank.setKnowledgeTreeList(knowledgeTreeNodes);
        return questionBank;
    }

    public static InputStream parseBase64ToInputStream(String base64Image) {
        try {
            // 移除Base64前缀
            String base64Clean = base64Image.split(",")[1]; // 适用于 "data:image/png;base64,..."
            byte[] decodedBytes = Base64.getDecoder().decode(base64Clean);
            return new ByteArrayInputStream(decodedBytes);
        } catch (Exception e) {
            throw new QhException("Base64解析失败", e);
        }
    }

    /**
     *  组装 题库,年级,章节,知识点等基础数据
     * @param libraryDTO libraryDTO
     * @param treeList treeList
     */
    public void bulidBaseData(QhLibraryInputDTO libraryDTO, List<QhKnowledgeTree> treeList) {

        // 组装题库数据
        QhKnowledgeTree libraryData = buildLibraryData(libraryDTO);
        treeList.add(libraryData);

        // 组装年级数据
        QhGradeInputDTO gradeDTO = libraryDTO.getGradeInputDTO();
        QhKnowledgeTree gradeData = buildGradeData(gradeDTO, libraryDTO.getLibraryId());
        treeList.add(gradeData);

        // 若题库可以不传章节，后续需要去除校验判空
        // 组装章节(递归)
        if (CollectionUtils.isNotEmpty(gradeDTO.getChapterInputDTOList())) {
            for (QhChapterInputDTO chapterDTO : gradeDTO.getChapterInputDTOList()) {
                buildChapterDatas(chapterDTO, gradeData, treeList);
            }
        }

        // 若题库可以不传知识点，后续需要去除校验判空
        // 组装知识点(从章节中获取,递归处理)
        List<QhKnowledgeInputDTO> knowledgeList = libraryDTO.getKnowledgeInputDTOList();
        if (CollectionUtils.isNotEmpty(knowledgeList)) {
            for (QhKnowledgeInputDTO knowledgeDTO : knowledgeList) {
                // 若知识点直接关联到章节, 需根据实际数据关联章节
                QhKnowledgeTree parentChapterData = findChapterData(knowledgeDTO.getChapterId(), treeList);
                if (ObjectUtil.isNotNull(parentChapterData)) {
                    buildKnowledgeDatas(knowledgeDTO, parentChapterData, treeList);
                }
            }
        }

    }

    private QhKnowledgeTree buildLibraryData(QhLibraryInputDTO libraryDTO) {
        QhKnowledgeTree libraryData = new QhKnowledgeTree();
        libraryData.setId(libraryDTO.getLibraryId());
        libraryData.setName(libraryDTO.getLibraryName());
        libraryData.setNodeType("1");
        libraryData.setAncestors("0"); // 祖级列表默认为0
        libraryData.setParentId("0");  // 父节点为0(根)
        libraryData.setShareType("2"); // 默认个人
        libraryData.setOrderNum(0);    // 显示顺序
        libraryData.setStatus("0");    // 状态正常
        libraryData.setDelFlag("0");   // 未删除
        return libraryData;
    }

    private QhKnowledgeTree buildGradeData(QhGradeInputDTO gradeDTO, String libraryId) {
        QhKnowledgeTree gradeData = new QhKnowledgeTree();
        gradeData.setId(gradeDTO.getGradeId());
        gradeData.setName(gradeDTO.getGradeName());
        gradeData.setNodeType("2");
        gradeData.setParentId(libraryId);         // 父节点为题库ID
        gradeData.setAncestors(libraryId);        // 祖级列表为题库ID
        gradeData.setOrderNum(0);
        gradeData.setStatus("0");
        gradeData.setDelFlag("0");
        return gradeData;
    }

    private void buildChapterDatas(QhChapterInputDTO chapterDTO, QhKnowledgeTree parentData, List<QhKnowledgeTree> treeList) {
        //父章节
        QhKnowledgeTree chapterData = new QhKnowledgeTree();
        chapterData.setId(chapterDTO.getChapterId());
        chapterData.setName(chapterDTO.getChapterName());
        chapterData.setNodeType("4");
        chapterData.setParentId(parentData.getId());
        chapterData.setAncestors(parentData.getAncestors() + "," + parentData.getId()); // 拼接祖级
        chapterData.setOrderNum(parentData.getOrderNum() + 1); //递增
        chapterData.setStatus("0");
        chapterData.setDelFlag("0");
        treeList.add(chapterData);

        // 递归处理子章节
        if (CollectionUtils.isNotEmpty(chapterDTO.getChildrenList())) {
            for (QhChapterInputDTO childChapter : chapterDTO.getChildrenList()) {
                buildChapterDatas(childChapter, chapterData, treeList);
            }
        }
    }

    private void buildKnowledgeDatas(QhKnowledgeInputDTO knowledgeDTO, QhKnowledgeTree parentChapterData, List<QhKnowledgeTree> treeList) {
        //父知识点
        QhKnowledgeTree knowledgeData = new QhKnowledgeTree();
        knowledgeData.setId(knowledgeDTO.getKnowledgePointId());
        knowledgeData.setName(knowledgeDTO.getKnowledgePointName());
        knowledgeData.setNodeType("5");
        knowledgeData.setParentId(parentChapterData.getId());
        knowledgeData.setAncestors(parentChapterData.getAncestors() + "," + parentChapterData.getId()); // 拼接祖级
        knowledgeData.setOrderNum(parentChapterData.getOrderNum() + 1);
        knowledgeData.setStatus("0");
        knowledgeData.setDelFlag("0");
        treeList.add(knowledgeData);

        // 递归处理子知识点
        if (CollectionUtils.isNotEmpty(knowledgeDTO.getChildrenList())) {
            for (QhKnowledgeInputDTO childKnowledge : knowledgeDTO.getChildrenList()) {
                buildKnowledgeDatas(childKnowledge, knowledgeData, treeList);
            }
        }
    }

    // 根据章节ID查找章节节点
    private QhKnowledgeTree findChapterData(String chapterId, List<QhKnowledgeTree> treeList) {
        return treeList.stream().filter(it -> StrUtil.equals("4", it.getNodeType()))
                .filter(item -> StrUtil.equals(chapterId, item.getId())).findFirst().orElse(null);
    }

    public void checkData(List<QhLibraryInputDTO> inputDTOList, Boolean flag) {
        if (CollectionUtils.isEmpty(inputDTOList)) {
            throw new QhException("入参不能为空，编码待定！");
        }
        for (QhLibraryInputDTO qhLibraryInputDTO : inputDTOList) {
            checkLibraryData(qhLibraryInputDTO, flag);
        }

    }

    private void checkLibraryData(QhLibraryInputDTO inputDTO, Boolean flag) {
        //判断是否是硬编码
        if (flag && StrUtil.isBlank(inputDTO.getLibraryId())) {
            throw new QhException("libraryId不能为空，编码待定！");
        }
        if (ObjectUtil.isNull(inputDTO.getGradeInputDTO())) {
            throw new QhException("年级信息不能为空，编码待定！");
        }
        QhGradeInputDTO gradeInputDTO = inputDTO.getGradeInputDTO();
        checkGradeData(gradeInputDTO, flag);

        if (CollectionUtils.isEmpty(inputDTO.getKnowledgeInputDTOList())) {
            throw new QhException("知识点不能为空，编码待定！");
        }
        checkKnowledgeData(inputDTO.getKnowledgeInputDTOList(), flag);
//        if (CollectionUtils.isEmpty(inputDTO.getKnowledgeQuestionDTOList())) {
//            throw new QhException("题目与知识点关联信息不能为空，编码待定！");
//        }
//        if (CollectionUtils.isEmpty(inputDTO.getQuestionInputDTOList())) {
//            throw new QhException("题目不能为空，编码待定！");
//        }
        checkQuestionData(inputDTO.getQuestionInputDTOList());
    }

    private void checkGradeData(QhGradeInputDTO inputDTO, Boolean flag) {
        //判断是否是硬编码
        if (flag && StrUtil.isBlank(inputDTO.getGradeId())) {
            throw new QhException("gradeId不能为空，编码待定！");
        }
        if (CollectionUtils.isEmpty(inputDTO.getChapterInputDTOList())) {
            throw new QhException("章节信息不能为空，编码待定！");
        }
        List<QhChapterInputDTO> chapterInputDTOList = inputDTO.getChapterInputDTOList();
        checkChapterData(chapterInputDTOList, flag);

    }

    private void checkChapterData(List<QhChapterInputDTO> inputDTOList, Boolean flag) {
        for (QhChapterInputDTO inputDTO : inputDTOList) {
            //判断是否是硬编码
            if (flag) {
                if (StrUtil.isBlank(inputDTO.getChapterId())) {
                    throw new QhException("chapter不能为空，编码待定！");
                }
                if (StrUtil.isBlank(inputDTO.getGradeId())) {
                    throw new QhException("gradeId不能为空，编码待定！");
                }
                //判断是否有子章节
                if (CollectionUtils.isNotEmpty(inputDTO.getChildrenList())) {
                    if (StrUtil.isBlank(inputDTO.getParentChapterId())) {
                        throw new QhException("根parentChapterId不能为空，要有默认值，比如0！");
                    }
                    //递归校验子章节
                    validateChapter(inputDTO);
                }
            } else {
                //validateChapter(inputDTO);
            }
        }
    }
    private void checkQuestionData(List<QhQuestionInputDTO> questionInputDTOList) {
        for (QhQuestionInputDTO inputDTO : questionInputDTOList) {
            if (StrUtil.isBlank(inputDTO.getQuestionDocument()) && StrUtil.isBlank(inputDTO.getQuestionImage())) {
                throw new QhException("题目文本和图片不能同时为空，编码待定！");
            }
            if (StrUtil.isBlank(inputDTO.getAnalysisDocument()) && StrUtil.isBlank(inputDTO.getAnalysisImage())) {
                throw new QhException("解析文本和图片不能同时为空，编码待定！");
            }
            if (StrUtil.isBlank(inputDTO.getAnswerDocument()) && StrUtil.isBlank(inputDTO.getAnswerImage())) {
                throw new QhException("答案文本和图片不能同时为空，编码待定！");
            }
            //题目的年级、章节、知识点判空待定
        }
    }


    private void checkKnowledgeData(List<QhKnowledgeInputDTO> knowledgeInputDTOList, Boolean flag) {
        for (QhKnowledgeInputDTO inputDTO : knowledgeInputDTOList) {
            //判断是否是硬编码
            if (flag) {
                if (StrUtil.isBlank(inputDTO.getChapterId())) {
                    throw new QhException("chapter不能为空，编码待定！");
                }
                if (StrUtil.isBlank(inputDTO.getKnowledgePointId())) {
                    throw new QhException("knowledgePointId不能为空，编码待定！");
                }
                //判断是否有子知识点
                if (CollectionUtils.isNotEmpty(inputDTO.getChildrenList())) {
                    if (StrUtil.isBlank(inputDTO.getParentKnowledgePointId())) {
                        throw new QhException("根parentKnowledgePointId不能为空，要有默认值，比如0！");
                    }
                    //递归校验子知识点
                    validateKnowledge(inputDTO);
                }
            } else {
                //validateKnowledge(inputDTO);
            }
        }
    }

    private static boolean validateChapter(QhChapterInputDTO inputDTO) {
        if (ObjectUtil.isNull(inputDTO)) {
            return Boolean.TRUE;
        }
        List<QhChapterInputDTO> children = inputDTO.getChildrenList();
        if (CollectionUtils.isEmpty(children)) {
            return Boolean.TRUE;
        }

        String parentChapterId = inputDTO.getChapterId();
        for (QhChapterInputDTO child : children) {
            // 检查子章节的parentChapterId是否等于当前章节的chapterId
            if (!Objects.equals(child.getParentChapterId(), parentChapterId)) {
                return Boolean.FALSE;
            }
            // 递归检查子章节
            if (!validateChapter(child)) {
                return Boolean.FALSE;
            }
        }
        return Boolean.TRUE;
    }

    private static boolean validateKnowledge(QhKnowledgeInputDTO inputDTO) {
        if (ObjectUtil.isNull(inputDTO)) {
            return Boolean.TRUE;
        }
        List<QhKnowledgeInputDTO> children = inputDTO.getChildrenList();
        if (CollectionUtils.isEmpty(children)) {
            return Boolean.TRUE;
        }

        String parentKnowledgePointrId = inputDTO.getKnowledgePointId();
        for (QhKnowledgeInputDTO child : children) {
            // 检查子章节的parentKnowledgePointrId是否等于当前章节的knowledgePointrId
            if (!Objects.equals(child.getParentKnowledgePointId(), parentKnowledgePointrId)) {
                return Boolean.FALSE;
            }
            // 递归检查子知识点
            if (!validateKnowledge(child)) {
                return Boolean.FALSE;
            }
        }
        return Boolean.TRUE;
    }

    /**
     *  可删
     * @param knowledgePointIds knowledgePointIds
     * @param libraryInputDTO libraryInputDTO
     * @param knowledgeTreeNodes knowledgeTreeNodes
     */
    public void buildKnowledgeTree(List<String> knowledgePointIds,
                                   QhLibraryInputDTO libraryInputDTO,
                                   List<QhKnowledgeTree> knowledgeTreeNodes) {
        // 预加载章节和知识点的映射关系
        Map<String, QhChapterInputDTO> chapterMap = buildChapterMap(libraryInputDTO);
        Map<String, QhKnowledgeInputDTO> knowledgeMap = buildKnowledgeMap(libraryInputDTO.getKnowledgeInputDTOList());

        Set<String> chapterNames = new HashSet<>();
        Set<String> gradeNames = new HashSet<>();
        Set<String> libraryNames = new HashSet<>();

        for (String knowledgeId : knowledgePointIds) {
            QhKnowledgeInputDTO knowledgeDTO = knowledgeMap.get(knowledgeId);
            if (ObjectUtil.isNotNull(knowledgeDTO)) {
                // 构建知识点树节点
                QhKnowledgeTree knowledgeNode = new QhKnowledgeTree();
                knowledgeNode.setId(knowledgeDTO.getKnowledgePointId());
                knowledgeNode.setName(knowledgeDTO.getKnowledgePointName());
                knowledgeNode.setNodeType("5");

                // 解析章节、年级、题库信息
                QhChapterInputDTO chapterDTO = chapterMap.get(knowledgeDTO.getChapterId());
                if (ObjectUtil.isNotNull(chapterDTO)) {
                    chapterNames.add(chapterDTO.getChapterName());
                    knowledgeNode.setParentId(chapterDTO.getChapterId());

                    // 年级和题库信息
                    QhGradeInputDTO gradeDTO = libraryInputDTO.getGradeInputDTO();
                    gradeNames.add(gradeDTO.getGradeName());
                    libraryNames.add(libraryInputDTO.getLibraryName());
                }
                knowledgeTreeNodes.add(knowledgeNode);
            }
        }
        // 附加信息（章节、年级、题库名称）
        String remark =
                "所属题库: " + String.join(",", libraryNames) +
                        " | 年级: " + String.join(",", gradeNames) +
                        " | 章节: " + String.join(",", chapterNames);

    }
}
