package com.domino.common.qh.dto;

import lombok.Data;

import java.awt.image.BufferedImage;
import java.io.File;
import java.util.List;

/**
 * 试卷解析详情
 *
 * <AUTHOR>
 */
@Data
public class QhProcessPaperDetailDTO {
    /**
     * 类型 1 单选 2 多选 3 判断 4 解答 5 填空
     */
    private String questionType;

    /**
     * 类型 0 题目 1 答案或解析
     */
    private Integer category;

    /**
     * tag标签
     */
    private String tag;

    /**
     * 分数
     */
    private Integer score;

    /**
     * 文本
     */
    private String text;

    /**
     * 顺序
     */
    private Integer index;

    /**
     * 题目图片或答案图片地址
     */
    private String context;

    /**
     * 解析图片
     */
    private String questionAnalyze;

    /**
     * 答案图片
     */
    private String questionAnswer;

    /**
     * 识图解析文本
     */
    private String ocrText;

    /**
     * 试题难度
     */
    private String difficulty;

    /**
     * 题库
     */
    private String bankId;

    /**
     * 节点
     */
    private List<String> knowledgeTreeIds;

    /**
     * 左上x
     */
    private Double x1;
    /**
     * 左上y
     */
    private Double y1;
    /**
     * 右上x
     */
    private Double x2;
    /**
     * 右上y
     */
    private Double y2;
    /**
     * 右下x
     */
    private Double x3;
    /**
     * 右下y
     */
    private Double y3;
    /**
     * 左下x
     */
    private Double x4;
    /**
     * 左下y
     */
    private Double y4;

}

