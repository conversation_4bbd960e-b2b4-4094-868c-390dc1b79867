package com.domino.common.enums;

/**
 * 业务操作类型
 */
public enum QhPaperProcessType {
    XZ(1, "选择题"), DX(1, "单选题"), DUX(2, "多选题"), <PERSON>(3, "判断题"), J<PERSON>(4, "解答题"), TK(5, "填空题");

    private final Integer code;
    private final String name;

    QhPaperProcessType(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static Integer getType(String text, Integer nowType) {
        if (text.contains(QhPaperProcessType.XZ.getName())) {
            return QhPaperProcessType.XZ.getCode();
        } else if (text.contains(QhPaperProcessType.DX.getName())) {
            return QhPaperProcessType.DX.getCode();
        } else if (text.contains(QhPaperProcessType.DUX.getName())) {
            return QhPaperProcessType.DUX.getCode();
        } else if (text.contains(QhPaperProcessType.PD.getName())) {
            return QhPaperProcessType.PD.getCode();
        } else if (text.contains(QhPaperProcessType.JD.getName())) {
            return QhPaperProcessType.JD.getCode();
        } else if (text.contains(QhPaperProcessType.TK.getName())) {
            return QhPaperProcessType.TK.getCode();
        }
        return nowType;
    }

    public static String getName(Integer code) {
        for (QhPaperProcessType value : QhPaperProcessType.values()) {
            if (value.getCode().equals(code)) {
                return value.getName();
            }
        }
        return null;
    }

    public static Integer getCode(String name) {
        for (QhPaperProcessType value : QhPaperProcessType.values()) {
            if (value.getName().equals(name)) {
                return value.getCode();
            }
        }
        return null;
    }
}
