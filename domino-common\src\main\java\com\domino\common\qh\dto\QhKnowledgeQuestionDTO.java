package com.domino.common.qh.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 题目与知识点关联信息
 *
 */

@Data
public class QhKnowledgeQuestionDTO implements Serializable {
    private static final long serialVersionUID = -1715048306076500356L;

    @NotBlank(message = "题目ID不能为空")
    private String questionId;

    @NotBlank(message = "知识点ID不能为空")
    private String knowledgePointId;
}
