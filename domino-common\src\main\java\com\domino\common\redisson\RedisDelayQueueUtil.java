package com.domino.common.redisson;

import com.domino.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBlockingQueue;
import org.redisson.api.RDelayedQueue;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class RedisDelayQueueUtil {

    @Resource
    private RedissonClient redissonClient;

    /**
     * 添加到延迟队列
     *
     * @param value     队列值
     * @param delay     延迟时间
     * @param timeUnit  时间单位
     * @param queueCode 队列键
     * @param <T>       泛型
     */
    public <T> void addDelayQueue(@NotNull T value, @NotNull long delay, @NotNull TimeUnit timeUnit, @NotNull String queueCode) {
        if(delay <0 ){
            throw new RuntimeException("添加延时队列，时间设置有误");
        }
        if (StringUtils.isBlank(queueCode) || Objects.isNull(value)) {
            log.error("加入延迟队列失败");
            throw new RuntimeException("添加延时队列失败");
        }
        try {
            RBlockingQueue<Object> blockingFairQueue = redissonClient.getBlockingQueue(queueCode);
            // 开启客户端监听（必须调用），否者系统重启时拿不到已过期数据，要等到系统第一次调用getDelayedQueue方法时才能开启监听
            RDelayedQueue<Object> delayedQueue = redissonClient.getDelayedQueue(blockingFairQueue);
            delayedQueue.offer(value, delay, timeUnit);
            log.info("添加延时队列成功，队列键: {}，队列值: {}，延识时间: {}，当前延迟队列长度: {}，队列中元素如下: {}", queueCode, value, timeUnit.toSeconds(delay) + "秒", delayedQueue.size(), delayedQueue.toArray());
        } catch (Exception e) {
            log.error("添加延时队列失败 {}", e.getMessage());
            throw new RuntimeException("添加延时队列失败");
        }
    }

    /**
     * 获取延迟队列
     */
    public <T> T getDelayQueue(@NotNull String queueCode) {
        if (StringUtils.isBlank(queueCode)) {
            return null;
        }
        RBlockingQueue<Object> blockingFairQueue = redissonClient.getBlockingQueue(queueCode);
        RDelayedQueue<Object> delayedQueue = redissonClient.getDelayedQueue(blockingFairQueue);
        T value = (T) delayedQueue.poll();
        if (Objects.nonNull(value)) {
            log.info("延时队列元素出栈成功，队列键: {}，taskId: {}，当前延迟队列长度: {}，队列中剩余元素如下: {}", queueCode, value, delayedQueue.size(), delayedQueue.toArray());
        }
        return value;
    }

    /**
     * 删除指定队列中的消息
     */
    public boolean removeDelayedQueue(@NotNull Object value, @NotNull String queueCode) {
        if (StringUtils.isBlank(queueCode) | Objects.isNull(value)) {
            return false;
        }
        RBlockingQueue<Object> blockingFairQueue = redissonClient.getBlockingQueue(queueCode);
        RDelayedQueue<Object> delayedQueue = redissonClient.getDelayedQueue(blockingFairQueue);
        return delayedQueue.remove(value);
    }
}
