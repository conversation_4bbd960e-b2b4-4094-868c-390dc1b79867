-- 删除任务逻辑
local task_wait_audit = KEYS[1]
local task_wait_start = KEYS[2]
local task_running = KEYS[3]
local task_end = KEYS[4]
local task_running_scramble = KEYS[5]
local task_user_order_prefix = KEYS[6]
local task_user_query_new = KEYS[7]
local task_user_query_old = KEYS[8]
local taskId = ARGV[1]
local task_user_order = task_user_order_prefix .. ':' .. taskId

-- 0表示不需要更新缓存，1表示需要更新缓存
local isUpdate = 0

-- 从task_wait_audit这个Set集合中移除taskId
redis.call('SREM', task_wait_audit, taskId)

-- 从task_wait_start这个Set集合中移除taskId
if redis.call('SREM', task_wait_start, taskId) == 1 then
    isUpdate = 1
end

-- 从task_running这个Set集合中移除taskId
if redis.call('SREM', task_running, taskId) == 1 then
    isUpdate = 1
end

-- 从task_end这个Set集合中移除taskId
redis.call('SREM', task_end, taskId)

if isUpdate == 1 then
    -- 删除任务数量与用户任务记录
    redis.call('HDEL', task_running_scramble, taskId)
    redis.call('DEL', task_user_order)
    -- 更新用户查询缓存
    if redis.call('EXISTS', task_user_query_new) == 1 then
        -- 如果task_user_query_new存在，则删除旧的缓存并重命名新的缓存
        redis.call('DEL', task_user_query_old)
        redis.call('RENAME', task_user_query_new, task_user_query_old)
    end
end
