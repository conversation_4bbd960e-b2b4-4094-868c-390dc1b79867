package com.domino.qh.service.impl;

import com.domino.common.minio.MinioConfig;
import com.domino.common.minio.MinioUtil;
import com.domino.qh.service.MinioService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.InputStream;

@Service
public class MinioServiceImpl implements MinioService {

    @Resource
    private MinioConfig minIOConfig;

    @Override
    public String fileUpload(String fileName, InputStream in) throws Exception {
        MinioUtil.uploadFile(minIOConfig.getBucketName(), fileName, in);
        // 返回图片链接url
        return minIOConfig.getFileHost() + "/" + minIOConfig.getBucketName() + "/" + fileName;
    }
}
