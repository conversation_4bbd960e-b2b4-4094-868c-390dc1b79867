package com.domino.qh.service.impl;

import org.springframework.stereotype.Component;

import java.awt.*;
import java.awt.image.BufferedImage;

@Component
public class ImageProcessor {

    public BufferedImage cropImage(BufferedImage source, int x, int y, int width, int height) {
        // 确保坐标在有效范围内
        x = Math.max(0, x);
        y = Math.max(0, y);
        width = Math.min(width, source.getWidth() - x);
        height = Math.min(height, source.getHeight() - y);

        return source.getSubimage(x, y, width, height);
    }

}
