package com.domino.common.qh.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 *  试卷与题目关联信息
 *
 */

@Data
public class QhPaperQuestionDTO implements Serializable {
    private static final long serialVersionUID = -966678403261134781L;

    @NotBlank(message = "题目ID不能为空")
    private String questionId;

    @NotBlank(message = "试卷ID不能为空")
    private String paperId;

}
