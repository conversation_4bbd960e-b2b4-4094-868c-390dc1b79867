package com.domino.common.qh.domain;

import com.domino.common.annotation.Excel;
import com.domino.common.annotation.Excel.ColumnType;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 用户对象 qh_question_bank
 */
@Data
public class QhQuestionBank implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Excel(name = "题目序号", cellType = ColumnType.NUMERIC, prompt = "用户编号")
    private String id;

    /**
     * 年级
     */
    private String gradeId;

    /**
     * 题目类型
     */
    private String questionType;

    /**
     * 题干内容 (图片url)
     */
    private String context;

    /**
     * ocr解析题干内容
     */
    private String ocrText;

    /**
     * 题目解析
     */
    private String questionAnalyze;

    /**
     * 题目答案
     */
    private String questionAnswer;

    /**
     * 分数
     */
    private String score;

    /**
     * 难度
     */
    private String difficulty;

    /**
     * 试卷类型
     */
    private String paperType;

    /**
     * 试卷来源
     */
    private String sourcePaper;

    /**
     * 年份
     */
    private String year;

    /**
     * 区域
     */
    private String region;

    /**
     * 标签id
     */
    private String tag;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 试题状态：正常0、弃用2
     */
    private String status;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 节点名称（前端查询参数）
     */
    private String knowledgeTreeName;

    /**
     * 节点id集合（前端查询参数，暂时不使用）
     */
    private List<String> knowledgeTreeIds;

    /**
     * 难度（前端查询参数）
     */
    private List<String> difficultyList;

    /**
     * 题型（前端查询参数）
     */
    private List<String> questionTypeList;

    /**
     * 试卷类型（前端查询参数）
     */
    private List<String> paperTypeList;

    /**
     * 区域（前端查询参数）
     */
    private List<String> regionList;

    /**
     * 题目绑定的知识点集合，前端展示用
     */
    private List<QhKnowledgeTree> knowledgeTreeList;

    /**
     * 前端查询参数
     */
    private String beginTime;

    /**
     * 前端查询参数
     */
    private String endTime;

    /**
     * 前端查询参数
     */
    private List<String> yearList;

    /**
     * 前端展示
     */
    private Boolean inBoard;

    /**
     * 前端展示
     */
    private Boolean inExport;

    /**
     * 前端查询参数
     */
    private String keyword;

    private Integer pageNum;

    private Integer pageSize;
}
