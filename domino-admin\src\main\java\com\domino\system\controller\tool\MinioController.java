package com.domino.system.controller.tool;

import cn.hutool.core.util.StrUtil;
import com.domino.common.core.controller.BaseController;
import com.domino.common.core.domain.AjaxResult;
import com.domino.common.minio.MinioConfig;
import com.domino.common.minio.MinioUtil;
import com.domino.common.utils.SecurityUtils;
import com.domino.qh.service.MinioService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.Objects;

/**
 * Minio资源管理
 */

@Api("Minio资源管理")
@Slf4j
@RestController
public class MinioController extends BaseController {

    @Resource
    private MinioConfig minIOConfig;
    @Resource
    private MinioService minioService;

    @ApiOperation("文件上传")
    @PostMapping("/tool/minio/fileUpload")
    // @PreAuthorize("@ss.hasRole('domino:authorize:tester')")
    public AjaxResult fileUpload(@RequestParam("uploadFile") MultipartFile uploadFile) throws Exception {
        if (Objects.isNull(uploadFile)) {
            throw new RuntimeException("上传的文件内容为空，请重新尝试");
        }
        String fileName = uploadFile.getOriginalFilename();
        String url = minioService.fileUpload(fileName, uploadFile.getInputStream());
        return success(url);
    }

    @ApiOperation("文件下载")
    @GetMapping("/tool/minio/fileDownload")
    public void fileDownload(String fileName, HttpServletResponse response) throws Exception {
        // 校验文件url
        if (StrUtil.isBlank(fileName)) {
            throw new RuntimeException("url为空，请重新尝试");
        }

        // 获取当前用户信息
        Long userId = SecurityUtils.getUserId();
        if (Objects.isNull(userId)) {
            throw new RuntimeException("用户信息缺失!");
        }

        // 文件下载，需要先获取文件的大小，如果获取不到文件会抛出异常信息
        String fileStatusInfo = MinioUtil.getFileStatusInfo(minIOConfig.getBucketName(), fileName);
        log.info("用户 {} 下载文件: {} 文件信息为: {}", userId, fileName, fileStatusInfo);

        // 开始下载
        try (InputStream inputStream = MinioUtil.getObject(minIOConfig.getBucketName(), fileName)) {
            // 文件名进行编码
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            // 要想让浏览器弹出下载框，你后端要设置一下响应头信息
            response.setContentType("application/octet-stream");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);

            // 获取响应输出流
            ServletOutputStream outputStream = response.getOutputStream();
            // 文件内容写入响应输出流
            byte[] buffer = new byte[1024];

            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            outputStream.flush();
        }
    }
}
