package com.domino.common.qh.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class QhQuestionBoardOrder {

    @NotBlank(message = "ID不能为空")
    private String id;
    @Min(value = 1, message = "排序号必须大于0")
    private Integer orderNum;
}
