package com.domino.qh.mapper;

import com.domino.common.qh.domain.QhKnowledgeQuestion;
import com.domino.common.qh.domain.QhKnowledgeTree;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 试题表 数据层
 */
@Mapper
public interface QhKnowledgeQuestionMapper {

    /**
     * 查询节点与试题信息
     *
     * @param knowledgeQuestion 节点与试题信息
     * @return 结果
     */
    List<QhKnowledgeQuestion> selectKnowledgeQuestion(QhKnowledgeQuestion knowledgeQuestion);

    /**
     * 根据子节点批量查询试题信息
     *
     * @param knowledgeIds 节点与试题信息
     * @return 结果
     */
    List<QhKnowledgeQuestion> batchSelectBankByKids(@Param("knowledgeIds") List<String> knowledgeIds,
                                                    @Param("bankIds") List<String> bankIds);

    /**
     * 新增节点与试题信息
     *
     * @param knowledgeQuestion 节点与试题信息
     * @return 结果
     */
    int insertKnowledgeQuestion(QhKnowledgeQuestion knowledgeQuestion);

    /**
     * 更新节点与试题信息
     *
     * @param knowledgeQuestion 节点与试题信息
     * @return 结果
     */
    int updateKnowledgeQuestion(QhKnowledgeQuestion knowledgeQuestion);

    /**
     * 删除节点与试题信息
     *
     * @param knowledgeQuestion 节点与试题信息
     * @return 结果
     */
    int deleteKnowledgeQuestion(QhKnowledgeQuestion knowledgeQuestion);
    
    /**
     * 根据题目ID查询关联的知识点
     * 
     * @param questionId 题目ID
     * @return 知识点列表
     */
    List<QhKnowledgeTree> selectKnowledgeTreesByQuestionId(@Param("questionId") String questionId);
}
