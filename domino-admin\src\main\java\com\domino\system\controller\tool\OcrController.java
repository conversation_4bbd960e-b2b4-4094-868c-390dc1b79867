// package com.domino.system.controller.tool;
//
// import com.alibaba.druid.util.StringUtils;
// import com.domino.common.core.controller.BaseController;
// import com.domino.common.core.domain.AjaxResult;
// import com.domino.qh.service.OcrService;
// import io.swagger.annotations.Api;
// import io.swagger.annotations.ApiOperation;
// import lombok.extern.slf4j.Slf4j;
// import net.sourceforge.tess4j.TesseractException;
// import org.springframework.web.bind.annotation.*;
// import org.springframework.web.multipart.MultipartFile;
//
// import javax.annotation.Resource;
// import java.io.IOException;
// import java.util.Objects;
//
// /**
//  * OCR图像识别
//  */
// @Api("OCR图像识别")
// @Slf4j
// @RestController
// @RequestMapping("/tool/ocr")
// public class OcrController extends BaseController {
//
//     @Resource
//     private OcrService ocrService;
//
//     @ApiOperation("根据图片url识别文字")
//     @GetMapping("recognizeImageUrl")
//     // @PreAuthorize("@ss.hasRole('domino:authorize:tester')")
//     public AjaxResult recognizeImageUrl(@RequestParam("url") String url) {
//         try {
//             if (StringUtils.isEmpty(url)) {
//                 return success();
//             }
//             String content = ocrService.recognizeText(url);
//             return success(content);
//         } catch (Exception e) {
//             return error("识别失败：" + e.getMessage());
//         }
//     }
//
//     @ApiOperation("根据图片识别文字")
//     @PostMapping("/recognizeImageFile")
//     public AjaxResult recognizeImageFile(@RequestParam("file") MultipartFile file) {
//         try {
//             if (Objects.isNull(file)) {
//                 return success();
//             }
//             String content = ocrService.recognizeText(file.getInputStream());
//             return success(content);
//         } catch (IOException | TesseractException e) {
//             return error("识别失败：" + e.getMessage());
//         }
//     }
// }
