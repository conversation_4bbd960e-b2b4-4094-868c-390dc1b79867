package com.domino.common.qh.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import java.awt.image.BufferedImage;

@Data
public class QuestionItem {
    private int questionNumber;
    private int pageNumber;
    private String questionText;
    private String answerText;
    private String analysisText;

    @JsonIgnore
    private BufferedImage questionImage;
    @JsonIgnore
    private BufferedImage answerImage;
    @JsonIgnore
    private BufferedImage analysisImage;

    private String questionImagePath;
    private String answerImagePath;
    private String analysisImagePath;
    private String textFilePath;
}
