# 手动启动项目时使用
mkdir -p /mnt/data/domino_logs

# 构建镜像
docker build -t domino-admin:1.1 .

# 运行容器
docker run -d \
--name domino-admin \
-p 8080:8080 \
--network domino-net \
-v /mnt/data/domino_logs:/logs \
domino-admin:1.1 /bin/bash

# 模拟Jenkins参数传递
/mnt/data/java_qh/deploy.sh \
  'registry.cn-hangzhou.aliyuncs.com' \
  'domino-admin' \
  'domino-qh' \
  '202504101812-96' \
  '8080' \
  '/mnt/data/java_qh/logs' \
  '汤某人呀' \
  'Xx164352'
