// package com.domino.qh.service.impl;
//
// import cn.hutool.http.HttpRequest;
// import com.alibaba.fastjson.JSONObject;
// import com.domino.qh.service.OcrService;
// import lombok.extern.slf4j.Slf4j;
// import org.springframework.beans.factory.annotation.Value;
// import org.springframework.context.annotation.Primary;
// import org.springframework.stereotype.Service;
//
// import java.io.ByteArrayOutputStream;
// import java.io.IOException;
// import java.io.InputStream;
// import java.util.Base64;
//
// /**
//  * 使用阿里云的OCR识别，优先选择
//  */
//
// @Slf4j
// @Primary
// @Service
// public class OcrAliyunServiceImpl implements OcrService {
//
//     @Value("${ocr.aliyun.appCode}")
//     private String appcode;
//
//     String aliyunOcrUrl = "https://gjbsb.market.alicloudapi.com/ocrservice/advanced";
//
//     public String recognizeText(String imageUrl) {
//         return sendOcrPost("", imageUrl);
//     }
//
//     @Override
//     public String recognizeText(InputStream inputStream) throws IOException {
//         return sendOcrPost(inputStreamToBase64(inputStream), "");
//     }
//
//     /**
//      * 将 InputStream 转换为 Base64 字符串
//      *
//      * @param inputStream 图片输入流
//      * @return Base64 字符串
//      * @throws IOException 读取流失败时抛出
//      */
//     public static String inputStreamToBase64(InputStream inputStream) throws IOException {
//         // JDK 8 要求在 try 语句内声明资源变量
//         try (InputStream is = inputStream;
//              ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
//
//             byte[] buffer = new byte[1024];
//             int bytesRead;
//
//             // 读取流数据并写入缓冲区
//             while ((bytesRead = is.read(buffer)) != -1) {
//                 outputStream.write(buffer, 0, bytesRead);
//             }
//
//             // 将字节数组转换为 Base64 字符串
//             byte[] imageBytes = outputStream.toByteArray();
//             return Base64.getEncoder().encodeToString(imageBytes);
//         }
//     }
//
//     String sendOcrPost(String base64, String imageUrl) {
//         // {
//         //     //图像数据：base64编码，要求base64编码后大小不超过25M，最短边至少15px，最长边最大8192px，支持jpg/png/bmp格式，和url参数只能同时存在一个
//         //     "img": "",
//         //         //图像url地址：图片完整URL，URL长度不超过1024字节，URL对应的图片base64编码后大小不超过25M，最短边至少15px，最长边最大8192px，支持jpg/png/bmp格式，和img参数只能同时存在一个
//         //         "url": "",
//         //         //是否需要识别结果中每一行的置信度，默认不需要。 true：需要 false：不需要
//         //         "prob": false,
//         //         //是否需要单字识别功能，默认不需要。 true：需要 false：不需要
//         //         "charInfo": false,
//         //         //是否需要自动旋转功能，默认不需要。 true：需要 false：不需要
//         //         "rotate": false,
//         //         //是否需要表格识别功能，默认不需要。 true：需要 false：不需要
//         //         "table": false,
//         //         //字块返回顺序，false表示从左往右，从上到下的顺序，true表示从上到下，从左往右的顺序，默认false
//         //         "sortPage": false,
//         //         //是否需要去除印章功能，默认不需要。true：需要 false：不需要
//         //         "noStamp": false,
//         //         //是否需要图案检测功能，默认不需要。true：需要 false：不需要
//         //         "figure": false,
//         //         //是否需要成行返回功能，默认不需要。true：需要 false：不需要
//         //         "row": false,
//         //         //是否需要分段功能，默认不需要。true：需要 false：不需要
//         //         "paragraph": false,
//         //         // 图片旋转后，是否需要返回原始坐标，默认不需要。true：需要  false：不需要
//         //         "oricoord": true
//         // }
//         String body = String.format("{\"img\":\"%s\",\"url\":\"%s\",\"prob\":false,\"charInfo\":false,\"rotate\":false,\"table\":false}", base64, imageUrl);
//         String content = "";
//         try {
//             String resp = HttpRequest.post(aliyunOcrUrl)
//                     // 在header中的格式(中间是英文空格)为Authorization:APPCODE 83359fd73fe94948385f570e3c139105
//                     .header("Authorization", "APPCODE " + appcode)
//                     // 根据API的要求，定义相对应的Content-Type
//                     .header("Content-Type", "application/json; charset=UTF-8")
//                     .body(body)
//                     .execute().body();
//             JSONObject jsonObject = JSONObject.parseObject(resp);
//             // 识别到的图片内容
//             content = jsonObject.get("content").toString();
//         } catch (Exception e) {
//             log.error("阿里云OCR识别错误", e);
//         }
//         return content;
//     }
// }
