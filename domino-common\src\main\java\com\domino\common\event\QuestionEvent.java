package com.domino.common.event;

import com.domino.common.qh.document.QuestionDocument;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

import java.util.List;

/**
 * 题目事件
 */
@Getter
@Setter
public class QuestionEvent extends ApplicationEvent {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 事件类型
     */
    private QuestionEventType eventType;
    
    /**
     * 题目文档（单个操作时使用）
     */
    private QuestionDocument questionDocument;
    
    /**
     * 题目文档列表（批量操作时使用）
     */
    private List<QuestionDocument> questionDocuments;
    
    /**
     * 题目ID（删除操作时使用）
     */
    private String questionId;
    
    /**
     * 题目ID列表（批量删除时使用）
     */
    private List<String> questionIds;
    
    /**
     * 试卷名称（按试卷删除时使用）
     */
    private String paperName;
    
    /**
     * 操作用户
     */
    private String operateBy;
    
    /**
     * 操作时间戳
     */
    private Long operateTimestamp;
    
    /**
     * 额外参数
     */
    private Object extraData;

    /**
     * 搜索关键词（查询操作时使用）
     */
    private String searchKeyword;

    /**
     * 搜索结果数量（查询操作时使用）
     */
    private Long resultCount;

    /**
     * 查询耗时（毫秒）（查询操作时使用）
     */
    private Long duration;
    
    /**
     * 构造函数 - 单个题目操作
     */
    public QuestionEvent(Object source, QuestionEventType eventType, QuestionDocument questionDocument, String operateBy) {
        super(source);
        this.eventType = eventType;
        this.questionDocument = questionDocument;
        this.operateBy = operateBy;
        this.operateTimestamp = System.currentTimeMillis();
    }
    
    /**
     * 构造函数 - 批量题目操作
     */
    public QuestionEvent(Object source, QuestionEventType eventType, List<QuestionDocument> questionDocuments, String operateBy) {
        super(source);
        this.eventType = eventType;
        this.questionDocuments = questionDocuments;
        this.operateBy = operateBy;
        this.operateTimestamp = System.currentTimeMillis();
    }
    
    /**
     * 构造函数 - 删除操作
     */
    public QuestionEvent(Object source, QuestionEventType eventType, String questionId, String operateBy) {
        super(source);
        this.eventType = eventType;
        this.questionId = questionId;
        this.operateBy = operateBy;
        this.operateTimestamp = System.currentTimeMillis();
    }
    
    /**
     * 构造函数 - 批量删除操作
     */
    public QuestionEvent(Object source, QuestionEventType eventType, List<String> questionIds, String operateBy, boolean isBatchDelete) {
        super(source);
        this.eventType = eventType;
        this.questionIds = questionIds;
        this.operateBy = operateBy;
        this.operateTimestamp = System.currentTimeMillis();
    }
    
    /**
     * 构造函数 - 按试卷删除
     */
    public QuestionEvent(Object source, QuestionEventType eventType, String paperName, String operateBy, boolean isPaperDelete) {
        super(source);
        this.eventType = eventType;
        this.paperName = paperName;
        this.operateBy = operateBy;
        this.operateTimestamp = System.currentTimeMillis();
    }
    
    /**
     * 构造函数 - 系统操作（如重建索引）
     */
    public QuestionEvent(Object source, QuestionEventType eventType, String operateBy, Object extraData) {
        super(source);
        this.eventType = eventType;
        this.operateBy = operateBy;
        this.extraData = extraData;
        this.operateTimestamp = System.currentTimeMillis();
    }
    
    /**
     * 创建新增事件
     */
    public static QuestionEvent createInsertEvent(Object source, QuestionDocument questionDocument, String operateBy) {
        return new QuestionEvent(source, QuestionEventType.INSERT, questionDocument, operateBy);
    }
    
    /**
     * 创建更新事件
     */
    public static QuestionEvent createUpdateEvent(Object source, QuestionDocument questionDocument, String operateBy) {
        return new QuestionEvent(source, QuestionEventType.UPDATE, questionDocument, operateBy);
    }
    
    /**
     * 创建删除事件
     */
    public static QuestionEvent createDeleteEvent(Object source, String questionId, String operateBy) {
        return new QuestionEvent(source, QuestionEventType.DELETE, questionId, operateBy);
    }
    
    /**
     * 创建批量删除事件
     */
    public static QuestionEvent createBatchDeleteEvent(Object source, List<String> questionIds, String operateBy) {
        return new QuestionEvent(source, QuestionEventType.BATCH_DELETE, questionIds, operateBy, true);
    }
    
    /**
     * 创建批量新增事件
     */
    public static QuestionEvent createBatchInsertEvent(Object source, List<QuestionDocument> questionDocuments, String operateBy) {
        return new QuestionEvent(source, QuestionEventType.BATCH_INSERT, questionDocuments, operateBy);
    }
    
    /**
     * 创建重建索引事件
     */
    public static QuestionEvent createRebuildIndexEvent(Object source, String operateBy) {
        return new QuestionEvent(source, QuestionEventType.REBUILD_INDEX, operateBy, null);
    }
    
    /**
     * 创建同步数据事件
     */
    public static QuestionEvent createSyncDataEvent(Object source, String operateBy, Object extraData) {
        return new QuestionEvent(source, QuestionEventType.SYNC_DATA, operateBy, extraData);
    }
    
    @Override
    public String toString() {
        return "QuestionEvent{" +
                "eventType=" + eventType +
                ", questionId='" + questionId + '\'' +
                ", operateBy='" + operateBy + '\'' +
                ", operateTimestamp=" + operateTimestamp +
                '}';
    }
}
