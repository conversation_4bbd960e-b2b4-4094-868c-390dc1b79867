package com.domino.common.qh.dto;

import com.domino.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 试卷分析报告DTO
 */
@Data
public class QhPaperAnalysisDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 试卷名称
     */
    @Excel(name = "试卷名称")
    private String paperName;

    /**
     * 试卷类型
     */
    @Excel(name = "试卷类型")
    private String paperType;
    
    /**
     * 题目总数
     */
    @Excel(name = "题目总数")
    private Integer totalQuestions;
    
    /**
     * 总分值
     */
    @Excel(name = "总分值")
    private BigDecimal totalScore;
    
    /**
     * 知识点覆盖数
     */
    @Excel(name = "知识点覆盖数")
    private Integer knowledgePointCount;
    
    /**
     * 知识点覆盖率（百分比）
     */
    @Excel(name = "知识点覆盖率", suffix = "%")
    private BigDecimal knowledgeCoverageRate;
    
    /**
     * 难度分布（简单、正常、困难、挑战的题目数量）
     */
    @Excel(name = "简单题占比", suffix = "%")
    private BigDecimal easyPercentage;
    
    @Excel(name = "正常题占比", suffix = "%")
    private BigDecimal mediumPercentage;
    
    @Excel(name = "困难题占比", suffix = "%")
    private BigDecimal hardPercentage;
    
    @Excel(name = "挑战题占比", suffix = "%")
    private BigDecimal challengePercentage;
    
    /**
     * 题型分布比例
     */
    @Excel(name = "题型分布")
    private String questionTypeDistribution;
    
    /**
     * 各类题型的详细统计
     * key: 题型名称, value: 该题型的数量
     */
    private Map<String, Integer> questionTypeMap = new HashMap<>();
    
    /**
     * 各知识点的覆盖情况
     * key: 知识点ID, value: 知识点名称
     */
    private Map<String, String> knowledgePointMap = new HashMap<>();
    
    /**
     * 各难度等级的题目数量
     * key: 难度等级, value: 该难度的题目数量
     */
    private Map<String, Integer> difficultyMap = new HashMap<>();
    
    /**
     * 用于存储饼图数据的相关字段
     */
    private List<PieChartItem> difficultyPieData;
    private List<PieChartItem> questionTypePieData;
    
    /**
     * 内部类，用于表示饼图数据项
     */
    @Data
    public static class PieChartItem {
        private String name;
        private BigDecimal value;
        private BigDecimal percentage;
        
        public PieChartItem(String name, BigDecimal value, BigDecimal percentage) {
            this.name = name;
            this.value = value;
            this.percentage = percentage;
        }
    }
} 