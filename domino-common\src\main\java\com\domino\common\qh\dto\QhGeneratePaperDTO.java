package com.domino.common.qh.dto;

import lombok.Data;

import java.util.List;

/**
 *  组卷请求DTO
 *
 */

@Data
public class QhGeneratePaperDTO {

    private String cpaperName;  //试卷名称
    private String cpaperType;  //试卷名称
    private String cregion;  //地区
    private String cyear;
    private String paperStyle;
    private String mulu;

    private List<String> libraryIds;  //题库ID列表
    private List<QhQuestionDuoDTO> questionDuoDTOList;
    private List<QhQuestionDTO> questionDTOList;

    // 新增遗传算法参数配置
    private Integer populationSize = 50;
    private Double crossoverRate = 0.8;
    private Double mutationRate = 0.15;
    private Integer maxGeneration = 100;
    private Double difficultyWeight = 0.4;
    private Double coverageWeight = 0.3;
    private Double typeMatchWeight = 0.3;
}
