// package com.domino.web.kafka;
//
// import com.domino.web.service.ElasticSearchService;
// import lombok.extern.slf4j.Slf4j;
// import org.apache.kafka.clients.consumer.ConsumerRecord;
// import org.springframework.kafka.annotation.KafkaListener;
// import org.springframework.stereotype.Component;
//
// import javax.annotation.Resource;
// import java.util.List;
//
// @Slf4j
// @Component
// public class KafkaConsumer {
//
//     // @Resource
//     // private ElasticSearchService elasticSearchService;
//
//     /**
//      * 监听新增插入请求
//      * 因为开启了批量消费，所以此处必须用List来接收
//      * - spring.kafka.listener.type=batch 设置批量消费
//      * - spring.kafka.consumer.max-poll-records=2 批量消费每次最多消费多少条消息
//      */
//     @KafkaListener(topics = KafkaInitialConfiguration.test)
//     public void topicInsertWord(List<ConsumerRecord<String, String>> records) {
//         records.forEach(record -> {
//             log.info("test 开始消费消息，topic: {}，partition: {}，value: {}", record.topic(), record.partition(), record.value());
//             // elasticSearchService.deleteWordById(record.value());
//             log.info("test 消息消费成功，topic: {}，partition: {}，value: {}", record.topic(), record.partition(), record.value());
//         });
//     }
// }
//
