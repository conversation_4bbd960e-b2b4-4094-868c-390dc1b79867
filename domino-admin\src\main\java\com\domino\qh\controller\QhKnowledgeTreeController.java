package com.domino.qh.controller;

import com.domino.common.annotation.Log;
import com.domino.common.core.controller.BaseController;
import com.domino.common.core.domain.AjaxResult;
import com.domino.common.core.page.TableDataInfo;
import com.domino.common.enums.BusinessType;
import com.domino.common.qh.domain.QhKnowledgeTree;
import com.domino.common.utils.poi.ExcelUtil;
import com.domino.qh.service.IQhKnowledgeTreeService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 树结构
 */
@RestController
@RequestMapping("/qh/knowledgeTree")
public class QhKnowledgeTreeController extends BaseController {

    @Resource
    private IQhKnowledgeTreeService qhKnowledgeService;

    /**
     * 获取内容列表（分页）
     */
    @PreAuthorize("@ss.hasPermi('qh:knowledgeTree:query')")
    @GetMapping("/page")
    public TableDataInfo selectKnowledgeTreePage(QhKnowledgeTree query) {
        startPage();
        List<QhKnowledgeTree> knowledgelist = qhKnowledgeService.selectKnowledgeTreeList(query);
        return getDataTable(knowledgelist);
    }

    /**
     * 获取内容列表
     */
    @PreAuthorize("@ss.hasPermi('qh:knowledgeTree:query')")
    @GetMapping("/list")
    public AjaxResult selectKnowledgeTreeList(QhKnowledgeTree query) {
        List<QhKnowledgeTree> knowledgelist = qhKnowledgeService.selectKnowledgeTreeList(query);
        return success(knowledgelist);
    }

    /**
     * 获取内容树列表（下拉框使用）
     */
    @PreAuthorize("@ss.hasPermi('qh:knowledgeTree:query')")
    @GetMapping("/tree")
    public AjaxResult knowledgeTree(QhKnowledgeTree query) {
        return success(qhKnowledgeService.buildKnowledgeTreeSelect(query));
    }

    /**
     * 首页统计数据
     */
    @PreAuthorize("@ss.hasPermi('system:user:list')")
    @GetMapping("/dashboard")
    public AjaxResult dashboard(String beginTime, String endTime) {
        return success(qhKnowledgeService.dashboard(beginTime, endTime));
    }

    @Log(title = "题目管理", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('qh:knowledgeTree:query')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, QhKnowledgeTree query) {
        List<QhKnowledgeTree> list = qhKnowledgeService.selectKnowledgeTreeList(query);
        ExcelUtil<QhKnowledgeTree> util = new ExcelUtil<>(QhKnowledgeTree.class);
        util.exportExcel(response, list, "题库数据");
    }

    /**
     * 根据内容编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('qh:knowledgeTree:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable String id) {
        return success(qhKnowledgeService.selectKnowledgeById(id));
    }

    /**
     * 新增内容
     */
    @PreAuthorize("@ss.hasPermi('qh:knowledgeTree:update')")
    @Log(title = "内容管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody QhKnowledgeTree knowledge) {
        if (!qhKnowledgeService.checkKnowledgeNameUnique(knowledge)) {
            return error("新增内容'" + knowledge.getName() + "'失败，内容名称已存在");
        }
        knowledge.setCreateBy(getUsername());
        return toAjax(qhKnowledgeService.insertKnowledge(knowledge));
    }

    /**
     * 修改内容
     */
    @PreAuthorize("@ss.hasPermi('qh:knowledgeTree:update')")
    @Log(title = "内容管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody QhKnowledgeTree knowledge) {
        if (!qhKnowledgeService.checkUpdateDataAllowed(knowledge.getId())) {
            throw new RuntimeException("暂无对该内容的操作权限！");
        }

        String id = knowledge.getId();
        if (!qhKnowledgeService.checkKnowledgeNameUnique(knowledge)) {
            return error("修改内容'" + knowledge.getName() + "'失败，内容名称已存在");
        } else if (knowledge.getParentId().equals(id)) {
            return error("修改内容'" + knowledge.getName() + "'失败，上级内容不能是自己");
            // } else if (StringUtils.equals(UserConstants.DEPT_DISABLE, knowledge.getStatus()) && qhKnowledgeService.selectNormalChildrenKnowledgeById(id) > 0) {
            //     return error("该内容包含未停用的子内容！");
            // }
            // 如果是停用，需要把所有子节点都停用，并且需要在查询的时候，把停用状态的节点去掉
            // 如果是启用，需要把所有子节点都启用
        }
        knowledge.setUpdateBy(getUsername());
        return toAjax(qhKnowledgeService.updateKnowledge(knowledge));
    }

    /**
     * 删除内容
     */
    @PreAuthorize("@ss.hasPermi('qh:knowledgeTree:update')")
    @Log(title = "内容管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable String id) {
        if (!qhKnowledgeService.checkUpdateDataAllowed(id)) {
            throw new RuntimeException("暂无对该内容的操作权限！");
        }
        qhKnowledgeService.deleteKnowledgeById(id);
        return success();
    }

    /**
     * 根据nodeType获取题库、年级、章节和知识点（下拉框使用）
     */
    @PreAuthorize("@ss.hasPermi('qh:knowledgeTree:query')")
    @PostMapping("/nodeType")
    public AjaxResult knowledgeNodeTypeTree(@RequestBody QhKnowledgeTree query) {
        return success(qhKnowledgeService.buildKnowledgeTreeByNodeType(query));
    }
}
