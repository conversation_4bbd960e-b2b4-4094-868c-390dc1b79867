package com.domino.common.qh.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * 知识点信息
 *
 */

@Data
public class QhKnowledgeInputDTO implements Serializable {
    private static final long serialVersionUID = -5175363090214901224L;

    private String knowledgePointId;

    private String chapterId;

    @NotBlank(message = "知识点名称不能为空")
    private String knowledgePointName;

    private String parentKnowledgePointId;

    private List<QhKnowledgeInputDTO> childrenList;
}
