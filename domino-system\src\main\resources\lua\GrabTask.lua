-- 用户抢任务逻辑

-- 1. 参数列表
local task_wait_audit = KEYS[1]
local task_wait_start = KEYS[2]
local task_running = KEYS[3]
local task_end = KEYS[4]
local task_running_scramble = KEYS[5]
local task_user_order_prefix = KEYS[6]
local task_user_query_new = KEYS[7]
local task_user_query_old = KEYS[8]
local taskId = ARGV[1]
local userId = ARGV[2]
local task_user_order = task_user_order_prefix .. ':' .. taskId

-- 2. 脚本业务
-- 2.1 判断任务状态
local task_status = 0
if redis.call('SISMEMBER', task_running, taskId) == 0 then
    if redis.call('SISMEMBER', task_wait_start, taskId) == 1 then
        task_status = 1  -- 待开始
    elseif redis.call('SISMEMBER', task_end, taskId) == 1 then
        task_status = 2  -- 已结束
    else
        task_status = 3  -- 任务信息不存在
    end
end
if task_status ~= 0 then
    return task_status
end

-- 2.2 检查库存是否充足
local task_count = redis.call('HGET', task_running_scramble, taskId)
if task_count == false or tonumber(task_count) <= 0 then
    -- 库存不足，则下架任务
    redis.call('SREM', task_running, taskId)
    redis.call('SADD', task_end, taskId)
    -- 删除任务数量与用户任务记录
    redis.call('HDEL', task_running_scramble, taskId)
    redis.call('DEL', task_user_order)
    -- 更新用户查询缓存
    if redis.call('EXISTS', task_user_query_new) == 1 then
        redis.call('DEL', task_user_query_old)  -- 先删除旧的缓存，避免RENAME失败
        redis.call('RENAME', task_user_query_new, task_user_query_old)
    end
    return 4  -- 库存不存在或库存不足
end

-- 2.3 判断用户是否下过单
if redis.call('SISMEMBER', task_user_order, userId) == 1 then
    return 5  -- 用户重复下单
end

-- 2.4 扣减库存并下单
redis.call('HINCRBY', task_running_scramble, taskId, -1)
redis.call('SADD', task_user_order, userId)

-- 2.5 再次校验任务数量，如果抢完了，就下架任务
task_count = redis.call('HGET', task_running_scramble, taskId)
if task_count == false or tonumber(task_count) <= 0 then
    -- 库存不足，则下架任务
    redis.call('SREM', task_running, taskId)
    redis.call('SADD', task_end, taskId)
    -- 删除任务数量与用户任务记录
    redis.call('HDEL', task_running_scramble, taskId)
    redis.call('DEL', task_user_order)
    -- 更新用户查询缓存
    if redis.call('EXISTS', task_user_query_new) == 1 then
        redis.call('DEL', task_user_query_old)  -- 先删除旧的缓存，避免RENAME失败
        redis.call('RENAME', task_user_query_new, task_user_query_old)
    end
end

-- 返回成功
return 0
