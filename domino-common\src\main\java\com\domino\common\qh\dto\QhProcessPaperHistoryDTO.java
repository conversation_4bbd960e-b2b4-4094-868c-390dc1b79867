package com.domino.common.qh.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 试卷解析历史记录及状态
 */
@Data
public class QhProcessPaperHistoryDTO {

    /**
     * 试卷名称
     */
    private String title;

    /**
     * 上传时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date uploadTime;

    /**
     * 解析状态
     */
    private String status;

    /**
     * 试卷url
     */
    private String url;

}

