package com.domino.qh.service;

import com.domino.common.core.domain.TreeSelect;
import com.domino.common.qh.domain.QhKnowledgeTree;

import java.util.List;

/**
 * 内容管理 服务层
 */
public interface IQhKnowledgeTreeService {
    /**
     * 查询内容管理数据
     *
     * @param query 内容信息
     * @return 内容信息集合
     */
    public List<QhKnowledgeTree> selectKnowledgeTreeList(QhKnowledgeTree query);

    /**
     * 首页信息查询
     */
    public Integer dashboard(String beginTime, String endTime);

    /**
     * 查询内容树结构信息
     *
     * @param query 内容信息
     * @return 内容树信息集合
     */
    public List<TreeSelect> buildKnowledgeTreeSelect(QhKnowledgeTree query);

    /**
     * 构建前端所需要树结构
     *
     * @param qhKnowledgeTree 内容列表
     * @return 树结构列表
     */
    public List<QhKnowledgeTree> buildKnowledgeTree(List<QhKnowledgeTree> qhKnowledgeTree);

    /**
     * 构建前端所需要下拉树结构
     *
     * @param qhKnowledgeTree 内容列表
     * @return 下拉树结构列表
     */
    public List<TreeSelect> buildKnowledgeTreeSelect(List<QhKnowledgeTree> qhKnowledgeTree);

    /**
     * 根据内容ID查询信息
     *
     * @param id 内容ID
     * @return 内容信息
     */
    public QhKnowledgeTree selectKnowledgeById(String id);

    /**
     * 是否存在内容子节点
     *
     * @param id 内容ID
     * @return 结果
     */
    public boolean hasChildById(String id);

    /**
     * 校验内容名称是否唯一
     *
     * @param qhKnowledge 内容信息
     * @return 结果
     */
    public boolean checkKnowledgeNameUnique(QhKnowledgeTree qhKnowledge);

    /**
     * 校验是否允许查询操作
     *
     * @param id 内容id
     */
    public boolean checkQueryDataAllowed(String id);

    /**
     * 校验是否允许更新操作
     *
     * @param id 内容id
     */
    public boolean checkUpdateDataAllowed(String id);

    /**
     * 新增保存内容信息
     *
     * @param qhKnowledge 内容信息
     * @return 结果
     */
    public int insertKnowledge(QhKnowledgeTree qhKnowledge);

    /**
     * 修改保存内容信息
     *
     * @param qhKnowledge 内容信息
     * @return 结果
     */
    public int updateKnowledge(QhKnowledgeTree qhKnowledge);

    /**
     * 删除内容管理信息
     *
     * @param id 内容ID
     * @return 结果
     */
    public int deleteKnowledgeById(String id);

    List<TreeSelect> buildKnowledgeTreeByNodeType(QhKnowledgeTree query);

    /**
     * 根据题目ID列表查所属题库
     *
     * @param questionBankIdList 题目ID列表
     * @return 结果
     */
    List<String> queryLyByIds(List<String> questionBankIdList);
}
