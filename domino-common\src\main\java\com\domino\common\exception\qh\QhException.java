package com.domino.common.exception.qh;

public class QhException extends RuntimeException {

	private static final long serialVersionUID = 1L;
	private String code;

	public QhException() {
		super();
	}

	public QhException(String message) {
		super(message);
	}

	public QhException(String code, String message) {
		super(message);
		this.code = code;
	}

	public QhException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
		super(message, cause, enableSuppression, writableStackTrace);
	}

	public QhException(String message, Throwable cause) {
		super(message, cause);
	}

	public QhException(Throwable cause) {
		super(cause);
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

}
