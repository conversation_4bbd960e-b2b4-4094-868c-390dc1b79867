package com.domino.common.utils;

import com.itextpdf.io.font.PdfEncodings;
import com.itextpdf.io.image.ImageData;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.kernel.colors.ColorConstants;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.Border;
import com.itextpdf.layout.element.*;
import com.itextpdf.layout.element.Image;
import com.itextpdf.layout.property.HorizontalAlignment;
import com.itextpdf.layout.property.TextAlignment;
import com.itextpdf.layout.property.UnitValue;
import org.jfree.chart.ChartFactory;
import org.jfree.chart.ChartUtils;
import org.jfree.chart.JFreeChart;
import org.jfree.chart.plot.PiePlot;
import org.jfree.data.general.DefaultPieDataset;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.awt.*;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.MalformedURLException;
import java.util.List;
import java.util.Map;

public class PDFUtils {
    // 日志记录器
    private static final Logger logger = LoggerFactory.getLogger(PDFUtils.class);

    // 默认配置
    private static final DeviceRgb TITLE_COLOR = new DeviceRgb(50, 50, 50); // 深灰色标题
    private static final DeviceRgb TABLE_HEADER_COLOR = new DeviceRgb(240, 240, 240); // 浅灰色背景
    // iText内置的支持中文的字体
    private static final String[] BUILTIN_CHINESE_FONTS = {
            "STSong-Light", // 宋体
            "STSongStd-Light", // 标准宋体
            "STKaiti-Regular" // 楷体
    };
    // 多个中文字体路径，按优先级排序（作为备选）
    private static final String[] CHINESE_FONT_PATHS = {
            "fonts/simhei.ttf",
            "fonts/simsun.ttc",
            "fonts/simkai.ttf",
            "fonts/msyh.ttf",
            "/WEB-INF/fonts/simhei.ttf",
            "/WEB-INF/fonts/simsun.ttc"
    };
    private static final float DEFAULT_FONT_SIZE = 12f;

    // 可自定义的饼图颜色
    private static final Color[] PIE_CHART_COLORS = new Color[]{
            new Color(255, 153, 153),  // 浅红色（类似珊瑚红）
            new Color(153, 204, 153),  // 浅绿色（类似薄荷绿）
            new Color(153, 204, 255),  // 浅蓝色（类似天蓝色）
            new Color(255, 204, 153),  // 浅橙色（辅助色）
            new Color(204, 153, 255),  // 浅紫色（辅助色）
            new Color(170, 170, 170)   // 中性灰色（过渡色）
    };

    // 文档对象
    private final Document document;
    private final PdfFont font;

    // 构造方法私有化
    private PDFUtils(Document document, PdfFont font) {
        this.document = document;
        this.font = font;
    }

    /**
     * 创建PDFUtils实例
     * @param outputStream 输出流
     * @param pageSize 页面尺寸
     */
    public static PDFUtils create(OutputStream outputStream, PageSize pageSize) throws IOException {
        PdfWriter writer = new PdfWriter(outputStream);
        PdfDocument pdf = new PdfDocument(writer);
        Document doc = new Document(pdf, pageSize);

        // 首先尝试使用iText内置的中文字体
        PdfFont font = null;
        for (String fontName : BUILTIN_CHINESE_FONTS) {
            try {
                // 使用内置字体并启用中文编码
                font = PdfFontFactory.createFont(fontName, "UniGB-UCS2-H", true);
                logger.info("成功加载内置中文字体: {}", fontName);
                break;
            } catch (IOException e) {
                logger.warn("无法加载内置中文字体 {}: {}", fontName, e.getMessage());
                // 继续尝试下一个字体
            }
        }

        // 如果内置字体加载失败，尝试加载本地中文字体
        if (font == null) {
            logger.info("尝试加载本地中文字体");
            for (String fontPath : CHINESE_FONT_PATHS) {
                try {
                    font = PdfFontFactory.createFont(fontPath, PdfEncodings.IDENTITY_H, true);
                    logger.info("成功加载本地中文字体: {}", fontPath);
                    break;
                } catch (IOException e) {
                    logger.warn("无法加载本地中文字体 {}: {}", fontPath, e.getMessage());
                    // 继续尝试下一个字体
                }
            }
        }

        // 如果所有中文字体都加载失败，使用默认字体
        if (font == null) {
            logger.error("无法加载任何中文字体，将使用默认字体，中文可能无法正确显示");
            // 最后的后备方案：使用默认字体
            font = PdfFontFactory.createFont();
        }

        doc.setFont(font);
        return new PDFUtils(doc, font);
    }

    /**
     * 添加标题
     * @param text 标题文本
     * @param level 标题级别 (1-3)
     */
    public void addTitle(String text, int level) {
        float fontSize;
        switch (level) {
            case 1:
                fontSize = 22f;
                break;
            case 2:
                fontSize = 18f;
                break;
            case 3:
                fontSize = 16f;
                break;
            default:
                fontSize = DEFAULT_FONT_SIZE;
                break;
        }

        Paragraph p = new Paragraph(text)
                .setFontSize(fontSize)
                //.setBold()
                .setFontColor(TITLE_COLOR)
                .setTextAlignment(TextAlignment.CENTER)
                .setMarginBottom(20f);
        document.add(p);
    }

    /**
     * 添加段落
     * @param text 段落文本
     * @param indent 首行缩进字符数
     */
    public void addParagraph(String text, int indent) {
        Paragraph p = new Paragraph(text)
                .setFontSize(DEFAULT_FONT_SIZE)
                .setFirstLineIndent(indent * DEFAULT_FONT_SIZE)
                .setMarginBottom(12f);
        document.add(p);
    }

    /**
     * 创建表格
     * @param headers 表头数组
     * @param data 表格数据
     * @param colWidths 列宽比例数组
     */
    public void addTable(String[] headers, List<String[]> data, float[] colWidths) {
        Table table = new Table(UnitValue.createPercentArray(colWidths))
                .setWidth(UnitValue.createPercentValue(100))
                .setMarginTop(15f)
                .setMarginBottom(20f);

        // 添加表头
        for (String header : headers) {
            Cell cell = new Cell()
                    .add(new Paragraph(header))
                    .setBackgroundColor(TABLE_HEADER_COLOR)
                    .setFontColor(ColorConstants.BLACK)
                    .setTextAlignment(TextAlignment.CENTER);
            table.addHeaderCell(cell);
        }

        // 添加数据行
        for (String[] row : data) {
            for (String cellData : row) {
                table.addCell(new Cell()
                        .add(new Paragraph(cellData))
                        .setPadding(5)
                        .setBorder(Border.NO_BORDER));
            }
        }
        document.add(table);
    }

    /**
     * 添加饼图
     * @param title 图表标题
     * @param data 数据（标签-值）
     * @param width 图表宽度
     * @param height 图表高度
     */
    public void addPieChart(String title, Map<String, Double> data, float width, float height) throws IOException {
        document.add(new Paragraph(title)
                //.setBold()
                .setMarginBottom(10f));

        // 创建饼图数据集
        DefaultPieDataset<String> dataset = new DefaultPieDataset<>();
        for (Map.Entry<String, Double> entry : data.entrySet()) {
            dataset.setValue(entry.getKey(), entry.getValue());
        }

        // 创建饼图
        JFreeChart chart = ChartFactory.createPieChart(
                title,           // 图表标题
                dataset,         // 数据集
                true,           // 显示图例
                true,           // 显示工具提示
                false           // 不生成URL
        );

        // 设置饼图样式
        PiePlot plot = (PiePlot) chart.getPlot();
        plot.setBackgroundPaint(Color.WHITE);
        plot.setOutlinePaint(null);
        // 设置显示标签，显示百分比
        plot.setLabelGenerator(new org.jfree.chart.labels.StandardPieSectionLabelGenerator("{0} ({2})"));
        plot.setShadowPaint(null);     // 无阴影

        // 设置中文字体，解决中文显示问题
        java.awt.Font labelFont = new java.awt.Font("SimSun", java.awt.Font.PLAIN, 12);
        plot.setLabelFont(labelFont);
        chart.getLegend().setItemFont(labelFont);
        chart.getTitle().setFont(new java.awt.Font("SimSun", java.awt.Font.BOLD, 14));

        // 设置颜色
        for (int i = 0; i < dataset.getKeys().size(); i++) {
            plot.setSectionPaint(dataset.getKey(i), PIE_CHART_COLORS[i % PIE_CHART_COLORS.length]);
        }

        // 将图表转换为图片
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ChartUtils.writeChartAsPNG(baos, chart, (int) width, (int) height);
        byte[] imageBytes = baos.toByteArray();

        // 添加到PDF
        Image image = new Image(ImageDataFactory.create(imageBytes))
                .setWidth(width)
                .setHeight(height)
                .setHorizontalAlignment(HorizontalAlignment.CENTER)
                .setMarginBottom(20f);
        document.add(image);

        // 添加一些间距
        document.add(new Paragraph().setMarginBottom(20f));
    }

    /**
     * 添加图片
     * @param imagePath 图片路径
     * @param width 图片宽度
     * @param height 图片高度
     * @param alignment 对齐方式
     */
    public PDFUtils addImage(String imagePath, float width, float height,
                           HorizontalAlignment alignment) throws MalformedURLException {
        ImageData imageData = ImageDataFactory.create(imagePath);
        Image image = new Image(imageData)
                .setWidth(width)
                .setHeight(height)
                .setHorizontalAlignment(alignment)
                .setMarginBottom(15f);
        document.add(image);
        return this;
    }

    /**
     * 添加分页符
     */
    public PDFUtils addPageBreak() {
        document.add(new AreaBreak());
        return this;
    }

    /**
     * 完成文档生成并关闭资源
     */
    public void close() {
        document.close();
    }
}
