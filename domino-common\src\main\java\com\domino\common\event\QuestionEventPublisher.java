package com.domino.common.event;

import com.domino.common.exception.qh.QhException;
import com.domino.common.qh.document.QuestionDocument;
import com.domino.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 题目事件发布器
 * 支持同步和异步事件发布，异步方法使用DominoThreadPool线程池
 */
@Component
@Slf4j
@EnableAsync
public class QuestionEventPublisher {
    
    @Resource
    private ApplicationEventPublisher applicationEventPublisher;
    
    /**
     * 发布新增题目事件
     */
    public void publishInsertEvent(QuestionDocument questionDocument) {
        try {
            String operateBy = getCurrentUser();
            QuestionEvent event = QuestionEvent.createInsertEvent(this, questionDocument, operateBy);
            applicationEventPublisher.publishEvent(event);
            log.debug("[事件发布] 发布新增题目事件: {}", questionDocument.getId());
        } catch (Exception e) {
            throw new QhException("[事件发布] 发布新增题目事件失败: {}", e.getMessage());
        }
    }
    
    /**
     * 发布更新题目事件
     */
    public void publishUpdateEvent(QuestionDocument questionDocument) {
        try {
            String operateBy = getCurrentUser();
            QuestionEvent event = QuestionEvent.createUpdateEvent(this, questionDocument, operateBy);
            applicationEventPublisher.publishEvent(event);
            log.debug("[事件发布] 发布更新题目事件: {}", questionDocument.getId());
        } catch (Exception e) {
            throw new QhException("[事件发布] 发布更新题目事件失败: {}", e.getMessage());
        }
    }
    
    /**
     * 发布删除题目事件
     */
    public void publishDeleteEvent(String questionId) {
        try {
            String operateBy = getCurrentUser();
            QuestionEvent event = QuestionEvent.createDeleteEvent(this, questionId, operateBy);
            applicationEventPublisher.publishEvent(event);
            log.debug("[事件发布] 发布删除题目事件: {}", questionId);
        } catch (Exception e) {
            throw new QhException("[事件发布] 发布删除题目事件失败: {}", e.getMessage());
        }
    }
    
    /**
     * 发布批量删除题目事件
     */
    public void publishBatchDeleteEvent(List<String> questionIds) {
        try {
            String operateBy = getCurrentUser();
            QuestionEvent event = QuestionEvent.createBatchDeleteEvent(this, questionIds, operateBy);
            applicationEventPublisher.publishEvent(event);
            log.debug("[事件发布] 发布批量删除题目事件, 数量: {}", questionIds.size());
        } catch (Exception e) {
            throw new QhException("[事件发布] 发布批量删除题目事件失败: {}", e.getMessage());
        }
    }
    
    /**
     * 发布批量新增题目事件
     */
    public void publishBatchInsertEvent(List<QuestionDocument> questionDocuments) {
        try {
            String operateBy = getCurrentUser();
            QuestionEvent event = QuestionEvent.createBatchInsertEvent(this, questionDocuments, operateBy);
            applicationEventPublisher.publishEvent(event);
            log.debug("[事件发布] 发布批量新增题目事件, 数量: {}", questionDocuments.size());
        } catch (Exception e) {
            throw new QhException("[事件发布] 发布批量新增题目事件失败: {}", e.getMessage());
        }
    }
    
    /**
     * 发布重建索引事件
     */
    public void publishRebuildIndexEvent() {
        try {
            String operateBy = getCurrentUser();
            QuestionEvent event = QuestionEvent.createRebuildIndexEvent(this, operateBy);
            applicationEventPublisher.publishEvent(event);
            log.info("[事件发布] 发布重建索引事件, 操作人: {}", operateBy);
        } catch (Exception e) {
            throw new QhException("[事件发布] 发布重建索引事件失败: {}", e.getMessage());
        }
    }
    
    /**
     * 发布同步数据事件
     */
    public void publishSyncDataEvent(Object extraData) {
        try {
            String operateBy = getCurrentUser();
            QuestionEvent event = QuestionEvent.createSyncDataEvent(this, operateBy, extraData);
            applicationEventPublisher.publishEvent(event);
            log.info("[事件发布] 发布同步数据事件, 操作人: {}, 额外参数: {}", operateBy, extraData);
        } catch (Exception e) {
            throw new QhException("[事件发布] 发布同步数据事件失败: {}", e.getMessage());
        }
    }
    
    /**
     * 异步发布新增题目事件
     * 使用DominoThreadPool线程池执行异步任务
     */
    @Async("DominoThreadPool")
    public void publishInsertEventAsync(QuestionDocument questionDocument) {
        try {
            publishInsertEvent(questionDocument);
            log.debug("[事件发布] 异步发布新增题目事件成功: {}", questionDocument.getId());
        } catch (Exception e) {
            throw new QhException("[事件发布] 异步发布新增题目事件失败: {}", e.getMessage());
        }
    }

    /**
     * 异步发布更新题目事件
     * 使用DominoThreadPool线程池执行异步任务
     */
    @Async("DominoThreadPool")
    public void publishUpdateEventAsync(QuestionDocument questionDocument) {
        try {
            publishUpdateEvent(questionDocument);
            log.debug("[事件发布] 异步发布更新题目事件成功: {}", questionDocument.getId());
        } catch (Exception e) {
            throw new QhException("[事件发布] 异步发布更新题目事件失败: {}", e.getMessage());
        }
    }

    /**
     * 异步发布删除题目事件
     * 使用DominoThreadPool线程池执行异步任务
     */
    @Async("DominoThreadPool")
    public void publishDeleteEventAsync(String questionId) {
        try {
            publishDeleteEvent(questionId);
            log.debug("[事件发布] 异步发布删除题目事件成功: {}", questionId);
        } catch (Exception e) {
            throw new QhException("[事件发布] 异步发布删除题目事件失败: {}", e.getMessage());
        }
    }
    
    /**
     * 异步发布批量删除题目事件
     * 使用DominoThreadPool线程池执行异步任务
     */
    @Async("DominoThreadPool")
    public void publishBatchDeleteEventAsync(List<String> questionIds) {
        try {
            publishBatchDeleteEvent(questionIds);
            log.debug("[事件发布] 异步发布批量删除题目事件成功, 数量: {}", questionIds.size());
        } catch (Exception e) {
            throw new QhException("[事件发布] 异步发布批量删除题目事件失败: {}", e.getMessage());
        }
    }

    /**
     * 异步发布批量新增题目事件
     * 使用DominoThreadPool线程池执行异步任务
     */
    @Async("DominoThreadPool")
    public void publishBatchInsertEventAsync(List<QuestionDocument> questionDocuments) {
        try {
            publishBatchInsertEvent(questionDocuments);
            log.debug("[事件发布] 异步发布批量新增题目事件成功, 数量: {}", questionDocuments.size());
        } catch (Exception e) {
            throw new QhException("[事件发布] 异步发布批量新增题目事件失败: {}", e.getMessage());
        }
    }

    /**
     * 条件发布事件（根据配置决定是否发布）
     */
    public void publishEventConditionally(QuestionEventType eventType, Object data) {
        // 这里可以根据配置文件或者系统设置决定是否发布事件
        boolean eventEnabled = isEventEnabled(eventType);
        if (!eventEnabled) {
            log.debug("[事件发布] 事件类型 {} 已被禁用，跳过发布", eventType);
            return;
        }
        
        switch (eventType) {
            case INSERT:
                if (data instanceof QuestionDocument) {
                    publishInsertEvent((QuestionDocument) data);
                }
                break;
            case UPDATE:
                if (data instanceof QuestionDocument) {
                    publishUpdateEvent((QuestionDocument) data);
                }
                break;
            case DELETE:
                if (data instanceof String) {
                    publishDeleteEvent((String) data);
                }
                break;
            case BATCH_DELETE:
                if (data instanceof List) {
                    publishBatchDeleteEvent((List<String>) data);
                }
                break;
            case BATCH_INSERT:
                if (data instanceof List) {
                    publishBatchInsertEvent((List<QuestionDocument>) data);
                }
                break;
            case REBUILD_INDEX:
                publishRebuildIndexEvent();
                break;
            case SYNC_DATA:
                publishSyncDataEvent(data);
                break;
            default:
                log.warn("[事件发布] 不支持的事件类型: {}", eventType);
                break;
        }
    }
    
    /**
     * 获取当前操作用户
     */
    private String getCurrentUser() {
        try {
            return SecurityUtils.getUsername();
        } catch (Exception e) {
            return "system";
        }
    }
    
    /**
     * 检查事件类型是否启用
     */
    private boolean isEventEnabled(QuestionEventType eventType) {
        // 这里可以从配置文件或数据库中读取配置
        // 暂时默认所有事件都启用
        return true;
    }
}
