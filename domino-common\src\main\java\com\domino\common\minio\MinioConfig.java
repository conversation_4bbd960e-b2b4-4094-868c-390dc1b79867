package com.domino.common.minio;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "minio")
public class MinioConfig {

    private String endpoint;
    private String fileHost;
    private String accessKey;
    private String secretKey;
    private String bucketName;
    private Integer imgSize;
    private Integer fileSize;

    @Bean
    public MinioUtil minioClient() {
        return new MinioUtil(endpoint, bucketName, accessKey, secretKey, imgSize, fileSize);
    }
}
