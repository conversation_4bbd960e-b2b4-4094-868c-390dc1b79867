package com.domino.qh.service.impl;

import org.apache.poi.xwpf.usermodel.*;
import org.springframework.stereotype.Component;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.List;

@Component
public class WordDocumentConverter {

    private static final int DPI = 144;
    private static final int MARGIN = 50;

    public List<BufferedImage> convertToImages(File wordFile) throws Exception {
        List<BufferedImage> images = new ArrayList<>();

        try (FileInputStream fis = new FileInputStream(wordFile);
             XWPFDocument doc = new XWPFDocument(fis)) {

            // 简单固定A4尺寸
            int width = (int) (8.5 * DPI);
            int height = (int) (11 * DPI);

            BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            Graphics2D g2d = image.createGraphics();
            g2d.setColor(Color.WHITE);
            g2d.fillRect(0, 0, width, height);

            int y = MARGIN;
            for (XWPFParagraph p : doc.getParagraphs()) {
                String text = p.getText();
                if (!text.isEmpty()) {
                    g2d.setColor(Color.BLACK);
                    g2d.setFont(new Font("Arial", Font.PLAIN, 12));
                    g2d.drawString(text, MARGIN, y);
                    y += 15;
                }
            }

            g2d.dispose();
            images.add(image);
        }

        return images;
    }
}