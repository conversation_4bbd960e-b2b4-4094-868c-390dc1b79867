<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.domino.qh.mapper.QhKnowledgeQuestionMapper">

    <resultMap type="QhKnowledgeQuestion" id="knowledgeQuestionResult">
        <id property="id" column="id"/>
        <result property="knowledgeTreeId" column="knowledge_tree_id"/>
        <result property="questionBankId" column="question_bank_id"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="update_by"/>
    </resultMap>

    <resultMap type="QhKnowledgeTree" id="knowledgeTreeResult">
        <id property="id" column="id"/>
        <result property="parentId" column="parent_id"/>
        <result property="ancestors" column="ancestors"/>
        <result property="name" column="name"/>
        <result property="orderNum" column="order_num"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="selectKnowledgeQuestion" parameterType="QhKnowledgeQuestion" resultMap="knowledgeQuestionResult">
        select d.id,
        d.knowledge_tree_id,
        d.question_bank_id,
        d.del_flag,
        d.create_time,
        d.create_by,
        d.update_time,
        d.update_by
        from qh_knowledge_question d
        where d.del_flag = '0'
        <if test="knowledgeTreeId != null and knowledgeTreeId != ''">
            and d.knowledge_tree_id = #{knowledgeTreeId}
        </if>
        <if test="questionBankId != null and questionBankId != ''">
            and d.question_bank_id = #{questionBankId}
        </if>
    </select>

    <select id="batchSelectBankByKids" parameterType="java.util.List" resultMap="knowledgeQuestionResult">
        select distinct
        d.question_bank_id,
        d.knowledge_tree_id
        from qh_knowledge_question d
        where d.del_flag = '0'
        <if test="knowledgeIds != null and knowledgeIds.size() > 0">
        and d.knowledge_tree_id in
            <foreach collection="knowledgeIds" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="bankIds != null and bankIds.size() > 0">
        and d.question_bank_id in
            <foreach collection="bankIds" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectKnowledgeTreesByQuestionId" resultMap="knowledgeTreeResult">
        SELECT kt.id,
               kt.parent_id,
               kt.ancestors,
               kt.name,
               kt.order_num,
               kt.status,
               kt.del_flag,
               kt.create_by,
               kt.create_time,
               kt.update_by,
               kt.update_time
        FROM qh_knowledge_tree kt
        JOIN qh_knowledge_question kq ON kt.id = kq.knowledge_tree_id
        WHERE kq.question_bank_id = #{questionId}
            AND kt.del_flag = '0'
            AND kq.del_flag = '0'
    </select>

    <insert id="insertKnowledgeQuestion" parameterType="QhKnowledgeQuestion" useGeneratedKeys="true" keyProperty="id">
        insert into qh_knowledge_question(
        <if test="id != null and id != ''">id,</if>
        <if test="knowledgeTreeId != null and knowledgeTreeId != ''">knowledge_tree_id,</if>
        <if test="questionBankId != null and questionBankId != ''">question_bank_id,</if>
        <if test="delFlag != null and delFlag != ''">del_flag,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        create_time
        )values(
        <if test="id != null and id != ''">#{id},</if>
        <if test="knowledgeTreeId != null and knowledgeTreeId != ''">#{knowledgeTreeId},</if>
        <if test="questionBankId != null and questionBankId != ''">#{questionBankId},</if>
        <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        sysdate()
        )
    </insert>

    <update id="updateKnowledgeQuestion" parameterType="QhKnowledgeQuestion">
        update qh_knowledge_question
        <set>
            <if test="knowledgeTreeId != null and knowledgeTreeId != 0">knowledge_tree_id = #{knowledgeTreeId},</if>
            <if test="questionBankId != null and questionBankId != ''">question_bank_id = #{questionBankId},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = sysdate()
        </set>
        where id = #{id}
    </update>

    <delete id="deleteKnowledgeQuestion" parameterType="QhKnowledgeQuestion">
        update qh_knowledge_question
        set del_flag = '2' AND update_by = #{updateBy}
        where id = #{id}
           OR knowledge_tree_id = #{knowledgeTreeId}
           OR question_bank_id = #{questionBankId}
    </delete>
</mapper>
