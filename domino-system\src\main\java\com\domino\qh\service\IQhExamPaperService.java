package com.domino.qh.service;

import com.domino.common.qh.domain.QhExamPaper;
import com.domino.common.qh.domain.QhQuestionBank;
import com.domino.common.qh.dto.QhGeneratePaperDTO;
import com.domino.common.qh.dto.QhQuestionBankDTO;

import java.util.List;

/**
 * 组卷
 */
public interface IQhExamPaperService {

    List<QhQuestionBank> generatePaper(QhGeneratePaperDTO paperDTO);

    List<QhQuestionBank> pingxing(String id, String flag);

    /**
     * 查询试卷分类
     *
     * @param id 试卷分类主键
     * @return 试卷分类
     */
    List<QhQuestionBank> selectQhExamPaperById(String id);


    List<QhQuestionBank> selectQhExamPaperById(String id, String flag);


    /**
     * 查询试卷分类
     *
     * @param id 试卷分类主键
     * @return 试卷分类
     */
    QhExamPaper queryQhExamPaperById(String id);

    /**
     * 首页信息查询
     */
    public Integer dashboard(String beginTime, String endTime);

    /**
     * 查询试卷分类列表
     *
     * @param qhExamPaper 试卷分类
     * @return 试卷分类集合
     */
    List<QhExamPaper> selectQhExamPaperList(QhExamPaper qhExamPaper);

    /**
     * 新增试卷
     *
     * @param paperDTO 试卷创建
     * @return 结果
     */
    int insertQhExamPaper(QhGeneratePaperDTO paperDTO);

    /**
     * 修改试卷分类
     *
     * @param qhExamPaper 试卷分类
     * @return 结果
     */
    int updateQhExamPaper(QhExamPaper qhExamPaper);

    /**
     * 批量删除试卷分类
     *
     * @param ids 需要删除的试卷分类主键集合
     * @return 结果
     */
    int deleteQhExamPaperByIds(String[] ids, String flag);

    /**
     * 删除试卷分类信息
     *
     * @param id 试卷分类主键
     * @return 结果
     */
    int deleteQhExamPaperById(String id, String flag);

}
