package com.domino.common.utils;

import com.domino.common.qh.dto.QhPaperAnalysisDTO;
import com.domino.common.qh.dto.QhPaperAnalysisDTO.PieChartItem;
import com.itextpdf.kernel.geom.PageSize;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 试卷分析报告PDF导出工具
 */
public class PaperReportPdfExporter {
    private static final Logger logger = LoggerFactory.getLogger(PaperReportPdfExporter.class);

    /**
     * 导出试卷分析报告为PDF
     * @param response HttpServletResponse
     * @param report 分析报告数据
     * @throws IOException IO异常
     */
    public static void exportPdf(HttpServletResponse response, QhPaperAnalysisDTO report) throws IOException {
        if (report == null) {
            logger.error("无法导出PDF：分析报告数据为空");
            throw new IOException("分析报告数据为空");
        }

        try {
            // 设置PDF响应头
            response.setContentType("application/pdf");
            String fileName = "试卷分析报告_" + report.getPaperName() + ".pdf";
            // 使用URLEncoder进行编码，确保中文文件名正确显示
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8");
            // 处理空格问题，将加号替换回空格
            encodedFileName = encodedFileName.replaceAll("\\+", "%20");
            response.setHeader("Content-Disposition", "attachment; filename=" + encodedFileName);

            logger.info("开始生成PDF报告: {}", fileName);

            // 使用PDFUtils生成PDF内容
            PDFUtils pdfUtils = PDFUtils.create(response.getOutputStream(), PageSize.A4);

            // 1. 添加标题
            pdfUtils.addTitle("试卷分析报告", 1);

            // 2. 添加基本信息表格
            String[] headers = {"项目", "内容"};
            List<String[]> basicInfoData = new ArrayList<>();
            basicInfoData.add(new String[]{"试卷名称", report.getPaperName() != null ? report.getPaperName() : "未命名"});
            basicInfoData.add(new String[]{"试卷类型", report.getPaperType() != null ? report.getPaperType() : "未分类"});
            basicInfoData.add(new String[]{"题目总数", String.valueOf(report.getTotalQuestions())});
            basicInfoData.add(new String[]{"总分值", report.getTotalScore() != null ? report.getTotalScore().toString() : "0"});
            basicInfoData.add(new String[]{"知识点覆盖数", String.valueOf(report.getKnowledgePointCount())});
            basicInfoData.add(new String[]{"知识点覆盖率", (report.getKnowledgeCoverageRate() != null ? report.getKnowledgeCoverageRate() : "0") + "%"});

            pdfUtils.addTitle("基本信息", 2);
            pdfUtils.addTable(headers, basicInfoData, new float[]{1, 3});

            // 3. 添加难度分布信息
            pdfUtils.addTitle("难度分布", 2);
            pdfUtils.addParagraph("试卷的难度分布情况如下所示，包括简单、正常、困难和挑战题目的比例。", 2);

            // 难度分布表格
            List<String[]> difficultyData = new ArrayList<>();
            difficultyData.add(new String[]{"简单题占比", (report.getEasyPercentage() != null ? report.getEasyPercentage() : "0") + "%"});
            difficultyData.add(new String[]{"正常题占比", (report.getMediumPercentage() != null ? report.getMediumPercentage() : "0") + "%"});
            difficultyData.add(new String[]{"困难题占比", (report.getHardPercentage() != null ? report.getHardPercentage() : "0") + "%"});
            difficultyData.add(new String[]{"挑战题占比", (report.getChallengePercentage() != null ? report.getChallengePercentage() : "0") + "%"});

            pdfUtils.addTable(headers, difficultyData, new float[]{1, 3});

            // 难度分布饼图
            Map<String, Double> difficultyPieData = new HashMap<>();
            if (report.getDifficultyPieData() != null) {
                for (PieChartItem item : report.getDifficultyPieData()) {
                    if (item != null && item.getName() != null && item.getValue() != null) {
                        difficultyPieData.put(item.getName(), item.getValue().doubleValue());
                    }
                }
            }

            if (!difficultyPieData.isEmpty()) {
                pdfUtils.addPieChart("难度分布", difficultyPieData, 200, 200);
            } else {
                pdfUtils.addParagraph("无难度分布数据", 2);
            }

            // 4. 添加题型分布信息
            pdfUtils.addTitle("题型分布", 2);
            pdfUtils.addParagraph("试卷包含的各类题型比例分布如下所示。", 2);

            // 题型分布表格
            List<String[]> typeData = new ArrayList<>();
            if (report.getQuestionTypeMap() != null) {
                for (Map.Entry<String, Integer> entry : report.getQuestionTypeMap().entrySet()) {
                    BigDecimal percentage = null;
                    if (report.getQuestionTypePieData() != null) {
                        for (PieChartItem item : report.getQuestionTypePieData()) {
                            if (item.getName().equals(entry.getKey())) {
                                percentage = item.getPercentage();
                                break;
                            }
                        }
                    }
                    if (percentage == null) {
                        percentage = BigDecimal.ZERO;
                    }

                    typeData.add(new String[]{entry.getKey(), entry.getValue() + " 题 (" + percentage + "%)"});
                }
            }

            if (!typeData.isEmpty()) {
                pdfUtils.addTable(headers, typeData, new float[]{1, 3});
            } else {
                pdfUtils.addParagraph("无题型分布数据", 2);
            }

            // 题型分布饼图
            Map<String, Double> typePieData = new HashMap<>();
            if (report.getQuestionTypePieData() != null) {
                for (PieChartItem item : report.getQuestionTypePieData()) {
                    if (item != null && item.getName() != null && item.getValue() != null) {
                        typePieData.put(item.getName(), item.getValue().doubleValue());
                    }
                }
            }

            if (!typePieData.isEmpty()) {
                pdfUtils.addPieChart("题型分布", typePieData, 200, 200);
            }

            // 5. 添加知识点覆盖情况信息
            pdfUtils.addTitle("知识点覆盖", 2);
            pdfUtils.addParagraph("本试卷共覆盖 " + report.getKnowledgePointCount() + " 个知识点，覆盖率为 "
                    + (report.getKnowledgeCoverageRate() != null ? report.getKnowledgeCoverageRate() : "0") + "%。", 2);

            if (report.getKnowledgePointMap() != null && !report.getKnowledgePointMap().isEmpty()) {
                // 知识点列表表格
                String[] knowledgeHeaders = {"序号", "知识点名称"};
                List<String[]> knowledgeData = new ArrayList<>();

                for (Map.Entry<String, String> entry : report.getKnowledgePointMap().entrySet()) {
                    knowledgeData.add(new String[]{entry.getKey(), entry.getValue()});
                }

                pdfUtils.addTable(knowledgeHeaders, knowledgeData, new float[]{1, 3});
            }

            // 6. 结束并关闭文档
            pdfUtils.close();
            logger.info("PDF报告生成成功: {}", fileName);
        } catch (Exception e) {
            logger.error("PDF报告生成失败: {}", e.getMessage(), e);
            throw new IOException("生成PDF报告时发生错误: " + e.getMessage(), e);
        }
    }
}
