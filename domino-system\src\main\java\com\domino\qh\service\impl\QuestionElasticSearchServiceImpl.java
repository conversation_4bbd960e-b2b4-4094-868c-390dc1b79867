package com.domino.qh.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.domino.common.elasticsearch.ElasticSearchUtil;
import com.domino.common.elasticsearch.ElasticSearchMappingLoader;
import com.domino.common.enums.QhPaperProcessType;
import com.domino.common.event.QuestionEventPublisher;
import com.domino.common.exception.qh.QhException;
import com.domino.common.qh.document.QuestionDocument;
import com.domino.common.qh.dto.QhProcessPaperDTO;
import com.domino.common.qh.dto.QhProcessPaperDetailDTO;
import com.domino.common.qh.form.QuestionSearchForm;
import com.domino.qh.service.QuestionElasticSearchService;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightBuilder;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightField;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;

/**
 * 题目Elasticsearch服务实现类
 */
@Slf4j
@Service
public class QuestionElasticSearchServiceImpl implements QuestionElasticSearchService {

    @Resource
    private ElasticSearchUtil elasticSearchUtil;

    @Resource
    private RestHighLevelClient restHighLevelClient;

    @Resource
    private ElasticSearchMappingLoader mappingLoader;

    /**
     * 题目索引名称
     */
    private static final String QUESTION_INDEX = "qh_questions";

    /**
     * 逻辑删除字段名
     */
    private static final String LOGICAL_DELETE_FIELD = "isDeleted";

    /**
     * 未删除状态值
     */
    private static final Integer NOT_DELETED = 0;

    /**
     * 已删除状态值
     */
    private static final Integer DELETED = 1;

    @Override
    public boolean createQuestionIndex() throws IOException {
        try {
            // 优先使用配置类中的映射配置
            if (mappingLoader.isQuestionMappingEnabled()) {
                String mapping = mappingLoader.getQuestionMapping();
                if (mapping != null && !mapping.trim().isEmpty()) {
                    return elasticSearchUtil.createIndex(QUESTION_INDEX, mapping);
                }
            } else {
                log.info("题目映射配置已禁用");
            }
        } catch (Exception e) {
            log.warn("从配置类加载映射配置失败: {}", e.getMessage());
        }

        // 不使用映射配置，让ES自动推断
        log.warn("使用映射配置，让ES自动推断！");
        return elasticSearchUtil.createIndex(QUESTION_INDEX, "{}");
    }

    @Override
    public int batchSaveQuestions(QhProcessPaperDTO paper) throws IOException {
        if (paper == null || paper.getDetails() == null || paper.getDetails().isEmpty()) {
            return 0;
        }

        List<QhProcessPaperDetailDTO> questionDetails = paper.getDetails();
        String paperName = paper.getPaperName();

        // 检查索引是否存在，不存在则创建（支持配置文件和降级方案）
        if (!isQuestionIndexExist()) {
            try {
                createQuestionIndex();
            } catch (Exception e) {
                log.error("创建题目索引失败，将依赖ES自动创建: {}", e.getMessage());
                throw new QhException("创建题目索引失败，将依赖ES自动创建: {}", e.getMessage());
            }
        }

        BulkRequest bulkRequest = new BulkRequest();
        int successCount = 0;

        for (int i = 0; i < questionDetails.size(); i++) {
            QhProcessPaperDetailDTO detail = questionDetails.get(i);

            // 转换为ES文档，传入完整的试卷信息
            QuestionDocument questionDocument = convertToQuestionDocument(detail, paper, i + 1);

            // 生成唯一ID
            String documentId = UUID.randomUUID().toString().replaceAll("-", "");
            questionDocument.setId(documentId);

            // 创建索引请求
            IndexRequest indexRequest = new IndexRequest(QUESTION_INDEX)
                    .id(documentId).source(JSON.toJSONString(questionDocument), XContentType.JSON);
            bulkRequest.add(indexRequest);
        }

        try {
            BulkResponse bulkResponse = restHighLevelClient.bulk(bulkRequest, RequestOptions.DEFAULT);
            if (!bulkResponse.hasFailures()) {
                successCount = questionDetails.size();
                log.info("批量保存题目到ES成功，试卷：{}，题目数量：{}", paperName, successCount);
            } else {
                log.error("批量保存题目到ES部分失败，试卷：{}，错误信息：{}", paperName, bulkResponse.buildFailureMessage());
                // 计算成功的数量
                successCount = (int) bulkResponse.getItems().length - bulkResponse.getItems().length;
            }
        } catch (Exception e) {
            log.error("批量保存题目到ES失败，试卷：{}", paperName, e);
            throw e;
        }
        return successCount;
    }

    @Override
    public String saveQuestion(QuestionDocument questionDocument) throws IOException {
        // 检查索引是否存在，不存在则创建
        if (!isQuestionIndexExist()) {
            try {
                createQuestionIndex();
            } catch (Exception e) {
                log.warn("创建索引失败，依赖ES自动创建: {}", e.getMessage());
            }
        }

        if (questionDocument.getId() == null) {
            questionDocument.setId(UUID.randomUUID().toString().replaceAll("-", ""));
        }

        // 设置逻辑删除字段默认值
        if (questionDocument.getIsDeleted() == null) {
            questionDocument.setIsDeleted(NOT_DELETED);
        }

        JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(questionDocument));
        return elasticSearchUtil.addData(QUESTION_INDEX, jsonObject, questionDocument.getId());
    }

    @Override
    public void deleteQuestionById(String questionId) throws IOException {
        elasticSearchUtil.deleteDataById(QUESTION_INDEX, questionId);
    }

    @Override
    public void deleteQuestionsByPaperName(String paperName) throws IOException {
        // 构建查询条件
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(QueryBuilders.termQuery("paperName.keyword", paperName));
        searchSourceBuilder.size(1000); // 设置最大返回数量

        SearchRequest searchRequest = new SearchRequest(QUESTION_INDEX);
        searchRequest.source(searchSourceBuilder);

        try {
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);

            if (searchResponse.getHits().getTotalHits().value > 0) {
                BulkRequest bulkRequest = new BulkRequest();

                searchResponse.getHits().forEach(hit -> {
                    DeleteRequest deleteRequest = new DeleteRequest(QUESTION_INDEX, hit.getId());
                    bulkRequest.add(deleteRequest);
                });

                restHighLevelClient.bulk(bulkRequest, RequestOptions.DEFAULT);
                log.info("删除试卷相关题目成功，试卷：{}，删除数量：{}", paperName, searchResponse.getHits().getTotalHits().value);
            }
        } catch (Exception e) {
            log.error("删除试卷相关题目失败，试卷：{}", paperName, e);
            throw e;
        }
    }

    @Override
    public void logicalDeleteQuestionById(String questionId) throws IOException {
        try {
            // 构建更新文档，将isDeleted字段设置为1
            Map<String, Object> updateDoc = new HashMap<>();
            updateDoc.put(LOGICAL_DELETE_FIELD, DELETED);
            updateDoc.put("updateTime", new Date());

            elasticSearchUtil.updateDataById(updateDoc, QUESTION_INDEX, questionId);
            log.info("逻辑删除题目成功，题目ID：{}", questionId);
        } catch (Exception e) {
            log.error("逻辑删除题目失败，题目ID：{}", questionId, e);
            throw new IOException("逻辑删除题目失败", e);
        }
    }

    @Override
    public void logicalDeleteQuestionsByPaperName(String paperName) throws IOException {
        try {
            // 构建查询条件：查找指定试卷且未被逻辑删除的题目
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.must(QueryBuilders.termQuery("paperName.keyword", paperName));
            boolQuery.must(QueryBuilders.termQuery(LOGICAL_DELETE_FIELD, NOT_DELETED)); // 只查找未删除的题目

            searchSourceBuilder.query(boolQuery);
            searchSourceBuilder.size(1000); // 设置最大返回数量

            SearchRequest searchRequest = new SearchRequest(QUESTION_INDEX);
            searchRequest.source(searchSourceBuilder);

            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);

            if (searchResponse.getHits().getTotalHits().value > 0) {
                BulkRequest bulkRequest = new BulkRequest();

                searchResponse.getHits().forEach(hit -> {
                    // 构建更新请求，将isDeleted设置为1
                    Map<String, Object> updateDoc = new HashMap<>();
                    updateDoc.put(LOGICAL_DELETE_FIELD, DELETED);
                    updateDoc.put("updateTime", new Date());

                    UpdateRequest updateRequest = new UpdateRequest(QUESTION_INDEX, hit.getId())
                            .doc(updateDoc);
                    bulkRequest.add(updateRequest);
                });

                restHighLevelClient.bulk(bulkRequest, RequestOptions.DEFAULT);
                log.info("逻辑删除试卷相关题目成功，试卷：{}，删除数量：{}", paperName, searchResponse.getHits().getTotalHits().value);
            }
        } catch (Exception e) {
            log.error("逻辑删除试卷相关题目失败，试卷：{}", paperName, e);
            throw new IOException("逻辑删除试卷相关题目失败", e);
        }
    }

    @Override
    public void updateQuestion(QuestionDocument questionDocument) throws IOException {
        elasticSearchUtil.updateDataById(questionDocument, QUESTION_INDEX, questionDocument.getId());
    }

    @Override
    public QuestionDocument getQuestionById(String questionId) throws IOException {
        Map<String, Object> result = elasticSearchUtil.searchDataById(QUESTION_INDEX, questionId, null);
        if (result != null && !result.isEmpty()) {
            return JSONObject.parseObject(JSON.toJSONString(result), QuestionDocument.class);
        }
        return null;
    }

    @Override
    public boolean isQuestionIndexExist() throws IOException {
        return elasticSearchUtil.isIndexExist(QUESTION_INDEX);
    }

    @Override
    public boolean deleteQuestionIndex() throws IOException {
        try {
            // 检查索引是否存在
            if (!isQuestionIndexExist()) {
                log.warn("题目索引 {} 不存在，无需删除", QUESTION_INDEX);
                return true;
            }

            // 删除索引
            boolean deleted = elasticSearchUtil.deleteIndex(QUESTION_INDEX);

            if (deleted) {
                log.info("题目索引 {} 删除成功", QUESTION_INDEX);
            } else {
                log.error("题目索引 {} 删除失败", QUESTION_INDEX);
            }

            return deleted;

        } catch (Exception e) {
            log.error("删除题目索引失败: {}", e.getMessage(), e);
            throw new IOException("删除题目索引失败", e);
        }
    }

    /**
     * 将QhProcessPaperDetailDTO转换为QuestionDocument
     * 包含完整的试卷信息
     */
    private QuestionDocument convertToQuestionDocument(QhProcessPaperDetailDTO detail, QhProcessPaperDTO paper, int questionIndex) {
        QuestionDocument document = new QuestionDocument();

        // 题目基本信息
        document.setQuestionType(detail.getQuestionType());
        document.setQuestionTypeName(getQuestionTypeName(detail.getQuestionType()));
        document.setContext(detail.getContext());
        document.setOcrText(detail.getOcrText());
        document.setQuestionAnalyze(detail.getQuestionAnalyze());
        document.setScore(detail.getScore() != null ? detail.getScore().toString() : null);
        document.setDifficulty(detail.getDifficulty());
        document.setTag(detail.getTag());
        document.setQuestionIndex(questionIndex);
        document.setCategory(detail.getCategory());
        document.setX1(detail.getX1());
        document.setY1(detail.getY1());
        document.setY4(detail.getY4());
        document.setKnowledgeTreeIds(detail.getKnowledgeTreeIds());

        // 试卷基本信息 - 每道题目都包含完整的试卷信息
        document.setPaperName(paper.getPaperName());
        document.setPaperType(paper.getPaperType());
        document.setRegion(paper.getRegion());
        document.setYear(paper.getPyear());
        document.setGradeId(paper.getGrade());
        document.setSubject(paper.getSubject());

        // 设置创建信息
        //SecurityUtils.getUsername()
        document.setCreateBy("-1");
        document.setCreateTime(new Date());

        // 设置逻辑删除字段默认值
        document.setIsDeleted(NOT_DELETED);

        return document;
    }

    /**
     * 为查询条件添加逻辑删除过滤
     *
     * @param boolQuery 查询条件
     */
    private void addLogicalDeleteFilter(BoolQueryBuilder boolQuery) {
        boolQuery.filter(QueryBuilders.termQuery(LOGICAL_DELETE_FIELD, NOT_DELETED));
    }

    /**
     * 为查询条件添加逻辑删除过滤
     *
     * @param boolQuery 查询条件
     * @param includeDeleted 是否包含已删除数据
     */
    private void addLogicalDeleteFilter(BoolQueryBuilder boolQuery, boolean includeDeleted) {
        if (!includeDeleted) {
            addLogicalDeleteFilter(boolQuery);
        }
    }

    @Override
    public Map<String, Object> searchQuestions(QuestionSearchForm searchForm) throws IOException {
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

        // 构建查询条件
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

        // 根据参数决定是否包含已删除数据
        addLogicalDeleteFilter(boolQuery, searchForm.getIncludeDeleted() != null && searchForm.getIncludeDeleted());

        // 关键词搜索（在题干和解析中搜索）
        if (StringUtils.hasText(searchForm.getKeyword())) {
            BoolQueryBuilder keywordQuery = QueryBuilders.boolQuery();
            keywordQuery.should(QueryBuilders.matchQuery("ocrText", searchForm.getKeyword()))
                       .should(QueryBuilders.matchQuery("questionAnalyze", searchForm.getKeyword()))
                       .should(QueryBuilders.matchQuery("paperName", searchForm.getKeyword()));
            boolQuery.must(keywordQuery);
        }

        // 题目关联知识点过滤
        if (!CollectionUtils.isEmpty(searchForm.getKnowledgeTreeIds())) {
            boolQuery.filter(QueryBuilders.termsQuery("knowledgeTreeIds", searchForm.getKnowledgeTreeIds()));
        }

        // 题目类型过滤
        if (!CollectionUtils.isEmpty(searchForm.getQuestionTypes())) {
            boolQuery.filter(QueryBuilders.termsQuery("questionType", searchForm.getQuestionTypes()));
        }

        // 难度过滤
        if (!CollectionUtils.isEmpty(searchForm.getDifficulties())) {
            boolQuery.filter(QueryBuilders.termsQuery("difficulty", searchForm.getDifficulties()));
        }

        // 试卷类型过滤
        if (!CollectionUtils.isEmpty(searchForm.getPaperTypes())) {
            boolQuery.filter(QueryBuilders.termsQuery("paperType", searchForm.getPaperTypes()));
        }

        // 年份过滤
        if (!CollectionUtils.isEmpty(searchForm.getYears())) {
            boolQuery.filter(QueryBuilders.termsQuery("year", searchForm.getYears()));
        }

        // 区域过滤
        if (StringUtils.hasText(searchForm.getRegion())) {
            boolQuery.filter(QueryBuilders.termsQuery("region", searchForm.getRegion()));
        }

        // 年级过滤
        if (!CollectionUtils.isEmpty(searchForm.getGradeIds())) {
            boolQuery.filter(QueryBuilders.termsQuery("gradeId", searchForm.getGradeIds()));
        }

        // 学科过滤
        if (!CollectionUtils.isEmpty(searchForm.getSubjects())) {
            boolQuery.filter(QueryBuilders.termsQuery("subject", searchForm.getSubjects()));
        }

        // 题目过滤
//        if (!CollectionUtils.isEmpty(searchForm.getKnowledgeTreeIds())) {
//            boolQuery.filter(QueryBuilders.termsQuery("id", searchForm.getKnowledgeTreeIds()));
//        }

        // 试卷来源过滤
        if (!CollectionUtils.isEmpty(searchForm.getPaperSources())) {
            boolQuery.filter(QueryBuilders.termQuery("sourcePaper", searchForm.getPaperSources()));
        }

        // 创建者过滤
        if (StringUtils.hasText(searchForm.getCreateBy())) {
            boolQuery.filter(QueryBuilders.termQuery("createBy", searchForm.getCreateBy()));
        }

        // 题目分类过滤
        if (searchForm.getCategory() != null) {
            boolQuery.filter(QueryBuilders.termQuery("category", searchForm.getCategory()));
        }

        // 试题标签过滤
        if (!CollectionUtils.isEmpty(searchForm.getTags())) {
            boolQuery.filter(QueryBuilders.termsQuery("tag.keyword", searchForm.getTags()));
        }

        // 分数范围过滤
        if (searchForm.getMinScore() != null || searchForm.getMaxScore() != null) {
            BoolQueryBuilder scoreQuery = QueryBuilders.boolQuery();
            if (searchForm.getMinScore() != null) {
                scoreQuery.filter(QueryBuilders.rangeQuery("score").gte(searchForm.getMinScore()));
            }
            if (searchForm.getMaxScore() != null) {
                scoreQuery.filter(QueryBuilders.rangeQuery("score").lte(searchForm.getMaxScore()));
            }
            boolQuery.filter(scoreQuery);
        }

        searchSourceBuilder.query(boolQuery);

        // 分页
        int from = (searchForm.getPageNum() - 1) * searchForm.getPageSize();
        searchSourceBuilder.from(from);
        searchSourceBuilder.size(searchForm.getPageSize());

        // 排序
        if (StringUtils.hasText(searchForm.getSortField())) {
            SortOrder sortOrder = "asc".equalsIgnoreCase(searchForm.getSortOrder()) ?
                SortOrder.ASC : SortOrder.DESC;
            searchSourceBuilder.sort(searchForm.getSortField(), sortOrder);
        }

        // 高亮设置
        if (!CollectionUtils.isEmpty(searchForm.getHighlightFields())) {
            HighlightBuilder highlightBuilder = new HighlightBuilder();
            for (String field : searchForm.getHighlightFields()) {
                highlightBuilder.field(field);
            }
            highlightBuilder.preTags("<em>");
            highlightBuilder.postTags("</em>");
            searchSourceBuilder.highlighter(highlightBuilder);
        }

        // 执行搜索
        SearchRequest searchRequest = new SearchRequest(QUESTION_INDEX);
        searchRequest.source(searchSourceBuilder);

        SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);

        // 解析结果
        List<Map<String, Object>> results = new ArrayList<>();
        for (SearchHit hit : searchResponse.getHits().getHits()) {
            Map<String, Object> sourceMap = hit.getSourceAsMap();
            sourceMap.put("id", hit.getId());

            // 确保包含关键字段
            ensureRequiredFields(sourceMap);

            // 处理高亮
            Map<String, HighlightField> highlightFields = hit.getHighlightFields();
            if (!highlightFields.isEmpty()) {
                Map<String, String> highlights = new HashMap<>();
                for (Map.Entry<String, HighlightField> entry : highlightFields.entrySet()) {
                    String fieldName = entry.getKey();
                    HighlightField highlightField = entry.getValue();
                    if (highlightField.getFragments().length > 0) {
                        highlights.put(fieldName, highlightField.getFragments()[0].toString());
                    }
                }
                sourceMap.put("highlights", highlights);
            }

            results.add(sourceMap);
        }

        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        long totalHits = searchResponse.getHits().getTotalHits().value;
        result.put("total", totalHits);
        result.put("pageNum", searchForm.getPageNum());
        result.put("pageSize", searchForm.getPageSize());
        result.put("pages", (totalHits + searchForm.getPageSize() - 1) / searchForm.getPageSize());
        result.put("list", results);
        return result;
    }



    /**
     * 确保返回结果包含必要的字段
     */
    private void ensureRequiredFields(Map<String, Object> sourceMap) {
        // 确保sourcePaper字段存在
        if (!sourceMap.containsKey("sourcePaper")) {
            sourceMap.put("sourcePaper", null);
        }

        // 确保region字段存在
        if (!sourceMap.containsKey("region")) {
            sourceMap.put("region", null);
        }

        // 确保year字段存在
        if (!sourceMap.containsKey("year")) {
            sourceMap.put("year", null);
        }

        // 确保其他常用字段存在
        if (!sourceMap.containsKey("paperName")) {
            sourceMap.put("paperName", null);
        }

        if (!sourceMap.containsKey("paperType")) {
            sourceMap.put("paperType", null);
        }

        if (!sourceMap.containsKey("subject")) {
            sourceMap.put("subject", null);
        }

        if (!sourceMap.containsKey("difficulty")) {
            sourceMap.put("difficulty", null);
        }

        if (!sourceMap.containsKey("questionType")) {
            sourceMap.put("questionType", null);
        }

        if (!sourceMap.containsKey("score")) {
            sourceMap.put("score", null);
        }

        if (!sourceMap.containsKey("tag")) {
            sourceMap.put("tag", null);
        }

        if (!sourceMap.containsKey("category")) {
            sourceMap.put("category", null);
        }

        if (!sourceMap.containsKey("ocrText")) {
            sourceMap.put("ocrText", null);
        }

        if (!sourceMap.containsKey("questionAnalyze")) {
            sourceMap.put("questionAnalyze", null);
        }
    }

    /**
     * 根据题目类型代码获取题目类型名称
     */
    private String getQuestionTypeName(String questionType) {
        if (questionType == null) {
            return null;
        }

        try {
            Integer typeCode = Integer.parseInt(questionType);
            return QhPaperProcessType.getName(typeCode);
        } catch (NumberFormatException e) {
            return questionType;
        }
    }
}
