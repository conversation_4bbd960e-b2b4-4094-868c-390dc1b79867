package com.domino.web.form;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ElasticSearchForm implements Serializable {

    private static final long serialVersionUID = -5713960829516633579L;

    /**
     * 查询的索引
     */
    private String index;

    /**
     * 查询的条件
     */
    private Map<String, String> searchConditions;

    /**
     * 排序字段
     */
    private List<String> sortField;

    /**
     * 高亮字段
     */
    private List<String> highlightField;

    /**
     * pageNum
     */
    private Integer pageNum;

    /**
     * pageSize
     */
    private Integer pageSize;
}
