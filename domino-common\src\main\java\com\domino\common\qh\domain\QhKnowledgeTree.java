package com.domino.common.qh.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.domino.common.annotation.Excel;
import com.domino.common.core.domain.entity.SysUser;
import com.domino.common.utils.SecurityUtils;
import com.domino.common.utils.StringUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 内容表 qh_knowledge_tree
 */
@Data
public class QhKnowledgeTree implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 内容ID
     */
    private String id;

    /**
     * 父内容ID
     */
    private String parentId;

    /**
     * 祖级列表
     */
    private String ancestors;

    /**
     * 内容名称
     */
    @Excel(name = "名称")
    private String name;

    /**
     * 显示顺序
     */
    private Integer orderNum;

    /**
     * 节点类型：1.题库，2.年级，3.学科，4.章节，5.知识点
     */
    @Excel(name = "类型", readConverterExp = "1=题库,2=年级,3=学科,4=章节,5=知识点")
    private String nodeType;

    /**
     * 如果节点是题库，则要设置共享类型：1.共享，2.个人
     */
    @Excel(name = "共享类型", readConverterExp = "1=共享,2=个人")
    private String shareType;

    /**
     * 内容状态:0正常,1停用
     */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 子节点
     */
    @TableField(exist = false)
    private List<String> ancestorIdList = new ArrayList<>();

    /**
     * 子节点
     */
    @TableField(exist = false)
    private List<QhKnowledgeTree> children = new ArrayList<>();

    @TableField(exist = false)
    private String copyFromTopNode;

    public List<String> getAncestorIdList() {
        if (StringUtils.isEmpty(this.ancestors)) {
            return Collections.emptyList();
        }
        return Arrays.stream(this.ancestors.split(",")).collect(Collectors.toList());
    }

    public boolean isTopNode() {
        return "0".equals(this.parentId);
    }

    /**
     * 是否为可访问的节点
     */
    public boolean isAccessibleQuery() {
        return SysUser.isAdmin(SecurityUtils.getUserId()) || "1".equals(this.shareType) || SecurityUtils.getUsername().equals(createBy);
    }

    /**
     * 是否为可更新的节点
     */
    public boolean isAccessibleUpdate() {
        return SysUser.isAdmin(SecurityUtils.getUserId()) || SecurityUtils.getUsername().equals(createBy);
    }
}


