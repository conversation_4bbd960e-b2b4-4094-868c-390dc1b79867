package com.domino.qh.controller;

import com.domino.common.annotation.Anonymous;
import org.springframework.web.bind.annotation.*;

/**
 * 需要在服务器开启IP白名单才能访问
 */
@RestController
public class QhMysqlBinlogController {

    @Anonymous
    @PostMapping("/mysql/binlog/questionBank/insert")
    public String insertQuestionBank(@RequestParam(name = "data") String data,
                                     @RequestHeader(value = "Authorization") String authorization) {
        System.out.println("method: post");
        System.out.println("authorization: " + authorization);
        System.out.println("data: " + data);
        return "ok";
    }

    @Anonymous
    @PutMapping("/mysql/binlog/questionBank/update")
    public String updateQuestionBank(@RequestParam(name = "data") String data,
                                     @RequestHeader(value = "Authorization") String authorization) {
        System.out.println("method: put");
        System.out.println("authorization: " + authorization);
        System.out.println("data: " + data);
        return "ok";
    }

    @Anonymous
    @DeleteMapping("/mysql/binlog/questionBank/delete/{id}")
    public String deleteQuestionBank(@PathVariable(name = "id") String id,
                                     @RequestHeader(value = "Authorization") String authorization) {
        System.out.println("method: delete");
        System.out.println("authorization: " + authorization);
        System.out.println("id: " + id);
        return "ok";
    }

    /**
     * ========================================================
     */

    @Anonymous
    @PostMapping("/mysql/binlog/knowledgeTree/insert")
    public String insertKnowledgeTree(@RequestParam(name = "data") String data,
                                      @RequestHeader(value = "Authorization") String authorization) {
        System.out.println("method: post");
        System.out.println("authorization: " + authorization);
        System.out.println("data: " + data);
        return "ok";
    }

    @Anonymous
    @PutMapping("/mysql/binlog/knowledgeTree/update")
    public String updateKnowledgeTree(@RequestParam(name = "data") String data,
                                      @RequestHeader(value = "Authorization") String authorization) {
        System.out.println("method: put");
        System.out.println("authorization: " + authorization);
        System.out.println("data: " + data);
        return "ok";
    }

    @Anonymous
    @DeleteMapping("/mysql/binlog/knowledgeTree/delete/{id}")
    public String deleteKnowledgeTree(@PathVariable(name = "id") String id,
                                      @RequestHeader(value = "Authorization") String authorization) {
        System.out.println("method: delete");
        System.out.println("authorization: " + authorization);
        System.out.println("id: " + id);
        return "ok";
    }


    /**
     * ========================================================
     */

    @Anonymous
    @PostMapping("/mysql/binlog/examPaper/insert")
    public String insertExamPaper(@RequestParam(name = "data") String data,
                                  @RequestHeader(value = "Authorization") String authorization) {
        System.out.println("method: post");
        System.out.println("authorization: " + authorization);
        System.out.println("data: " + data);
        return "ok";
    }

    @Anonymous
    @PutMapping("/mysql/binlog/examPaper/update")
    public String updateExamPaper(@RequestParam(name = "data") String data,
                                  @RequestHeader(value = "Authorization") String authorization) {
        System.out.println("method: put");
        System.out.println("authorization: " + authorization);
        System.out.println("data: " + data);
        return "ok";
    }

    @Anonymous
    @DeleteMapping("/mysql/binlog/examPaper/delete/{id}")
    public String deleteExamPaper(@PathVariable(name = "id") String id,
                                  @RequestHeader(value = "Authorization") String authorization) {
        System.out.println("method: delete");
        System.out.println("authorization: " + authorization);
        System.out.println("id: " + id);
        return "ok";
    }
}
