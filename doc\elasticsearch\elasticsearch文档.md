## word表



### 1、索引

```shell
# 创建索引，同时进行索引分片配置
PUT /word
{ 
  "settings": {
    "number_of_shards": 3, #指定主分片的数量，分片就是将数据分成几部分进行存储，这里是将数据分成3份进行存储，存储在不同的节点上
    "number_of_replicas": 1 #指定每个主分片的副本分片的数量
  }
}

# 查询索引：?v表示显示上面的标题
GET /_cat/indices?v

# 删除索引
DELETE /word
```



### 2、mapping

`index` 参数，用来控制当前字段是否被索引，默认为 `true`，如果设为 `false`，**则该字段不可被搜索**

```shell
# 创建索引&映射
PUT /word
{ 
  "settings": {
    "number_of_shards": 1,
    "number_of_replicas": 0
  }, 
  "mappings": {
    "properties": {
      "word":{
        "type": "text",
        "index": true
      },
      "phoneticSymbol":{
        "type": "keyword",
        "index": false
      },
      "paraphrase":{
        "type": "text",
        "index": true
      },
      "memoryAid":{
        "type": "keyword",
        "index": false
      },
      "sentence":{
        "type": "text",
        "index": true
      },
      "synonym":{
        "type": "text",
        "index": true
      },
      "similarWord":{
        "type": "text",
        "index": true
      },
      "associationWord":{
        "type": "text",
        "index": true
      },
      "difficultyLevel":{
        "type": "integer",
        "index": true
      },
      "createTime":{
        "type": "date",
        "index": false,
        "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis"
      },
      "updateTime":{
        "type": "date",
        "index": false,
        "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis"
      },
      "isDelete":{
        "type": "integer",
        "index": true
      }
    }
  }
}

# 查询mapping
GET /word/_mapping
```



### 3、文档

```shell
# 插入文档
POST /word/_doc/1862818595977621505
{
  "word":"nihao",
  "phoneticSymbol":"nihao",
  "paraphrase":"{n=aa, v=bb}",
  "memoryAid":"aa",
  "sentence":"[n, aa]",
  "synonym":"[n, aa]",  
  "similarWord":"[n, aa]",
  "associationWord":"[n, aa]",
  "difficultyLevel":"1",
  "createTime":"2024-11-30 17:14:10",
  "updateTime":null,
  "isDelete":"0"
}

# 根据id查询文档
GET /word/_doc/1862787191592013825
# 查询索引下所有文档
GET /word/_search
# 删除文档
DELETE /word/_doc/1862787191592013825

# 更新文档有两种方式，一种是删除文档再新增，一种是基于原始文档进行更新，推荐使用第一种
PUT /word/_doc/1862818595977621505
{
  "word":"ikun",
  "phoneticSymbol":"nihao",
  "paraphrase":"{n=aa, v=bb}",
  "memoryAid":"aa",
  "sentence":"[n, aa]",
  "synonym":"[n, aa]",  
  "similarWord":"[n, aa]",
  "associationWord":"[n, aa]",
  "difficultyLevel":"1",
  "createTime":"2024-11-30 17:14:10",
  "updateTime":null,
  "isDelete":"0"
}
```

