package com.domino.qh.service.impl;

import com.aspose.words.*;
import com.domino.common.exception.qh.QhException;
import com.domino.common.lib.CrackApose;
import com.domino.common.qh.domain.QhExamPaper;
import com.domino.common.qh.domain.QhQuestionBank;
import com.domino.common.qh.dto.QhPaperExportDTO;
import com.domino.common.utils.StringUtils;
import com.domino.qh.service.IQhExamPaperService;
import com.domino.qh.service.IQhPaperExportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URL;
import java.net.URLEncoder;
import java.util.List;

/**
 * 试卷导出服务实现
 *
 * <AUTHOR>
 * @date 2025-08-11
 */
@Slf4j
@Service
public class QhPaperExportServiceImpl implements IQhPaperExportService {

    @Autowired
    private IQhExamPaperService qhExamPaperService;

    @Override
    public void exportToPdf(QhPaperExportDTO exportDTO, HttpServletResponse response) {
        try {
            // 获取试卷数据
            QhExamPaper paper = qhExamPaperService.queryQhExamPaperById(exportDTO.getPaperId());
            if (paper == null) {
                throw new QhException("试卷不存在");
            }

            List<QhQuestionBank> questions = qhExamPaperService.selectQhExamPaperById(
                    exportDTO.getPaperId(), exportDTO.getFlag());
            if (CollectionUtils.isEmpty(questions)) {
                throw new QhException("试卷题目为空");
            }

            // 生成Word文档
            Document doc = generateWordDocument(paper, questions, exportDTO);

            // 转换为PDF并输出
            convertToPdfAndOutput(doc, paper.getPaperName(), response);

        } catch (Exception e) {
            log.error("导出PDF失败", e);
            throw new QhException("导出PDF失败: " + e.getMessage());
        }
    }

    @Override
    public void exportToWord(QhPaperExportDTO exportDTO, HttpServletResponse response) {
        try {
            // 获取试卷数据
            QhExamPaper paper = qhExamPaperService.queryQhExamPaperById(exportDTO.getPaperId());
            if (paper == null) {
                throw new QhException("试卷不存在");
            }

            List<QhQuestionBank> questions = qhExamPaperService.selectQhExamPaperById(
                    exportDTO.getPaperId(), exportDTO.getFlag());
            if (CollectionUtils.isEmpty(questions)) {
                throw new QhException("试卷题目为空");
            }

            // 生成Word文档
            Document doc = generateWordDocument(paper, questions, exportDTO);

            // 输出Word文档
            outputWordDocument(doc, paper.getPaperName(), response);

        } catch (Exception e) {
            log.error("导出Word失败", e);
            throw new QhException("导出Word失败: " + e.getMessage());
        }
    }



    /**
     * 生成Word文档
     */
    private Document generateWordDocument(QhExamPaper paper, List<QhQuestionBank> questions,
                                        QhPaperExportDTO exportDTO) throws Exception {
        // 初始化Aspose Words许可证
        CrackApose.initLicense();

        Document doc = new Document();
        DocumentBuilder builder = new DocumentBuilder(doc);

        // 设置页面格式
        setupPageFormat(builder, exportDTO.getFormatOptions());

        // 添加试卷标题
        addPaperTitle(builder, paper, exportDTO.getFormatOptions());

        // 添加试卷信息
        addPaperInfo(builder, paper, exportDTO.getFormatOptions());

        // 添加题目
        if (exportDTO.getContentOptions().getIncludeQuestions()) {
            addQuestions(builder, questions, exportDTO);
        }

        return doc;
    }

    /**
     * 设置页面格式
     */
    private void setupPageFormat(DocumentBuilder builder, QhPaperExportDTO.FormatOptions formatOptions) {
        if (formatOptions == null) {
            formatOptions = new QhPaperExportDTO.FormatOptions();
        }

        PageSetup pageSetup = builder.getPageSetup();

        // 设置页面大小
        switch (formatOptions.getPageSize()) {
            case "A3":
                pageSetup.setPaperSize(PaperSize.A3);
                break;
            case "Letter":
                pageSetup.setPaperSize(PaperSize.LETTER);
                break;
            default:
                pageSetup.setPaperSize(PaperSize.A4);
                break;
        }

        // 设置页边距（毫米转换为磅）
        double margin = formatOptions.getMargin() * 2.83465; // 1mm = 2.83465 points
        pageSetup.setTopMargin(margin);
        pageSetup.setBottomMargin(margin);
        pageSetup.setLeftMargin(margin);
        pageSetup.setRightMargin(margin);

        // 设置默认字体
        Font font = builder.getFont();
        font.setName("宋体");
        font.setSize(formatOptions.getFontSize());

        // 设置段落格式
        ParagraphFormat paragraphFormat = builder.getParagraphFormat();
        paragraphFormat.setLineSpacing(formatOptions.getLineSpacing() * 12); // 行间距
    }

    /**
     * 添加试卷标题
     */
    private void addPaperTitle(DocumentBuilder builder, QhExamPaper paper,
                              QhPaperExportDTO.FormatOptions formatOptions) throws Exception {
        // 设置标题样式
        builder.getFont().setSize(formatOptions.getFontSize() + 4);
        builder.getFont().setBold(true);
        builder.getParagraphFormat().setAlignment(ParagraphAlignment.CENTER);

        // 添加标题
        builder.writeln(paper.getPaperName());
        builder.writeln();

        // 恢复正常样式
        builder.getFont().setSize(formatOptions.getFontSize());
        builder.getFont().setBold(false);
        builder.getParagraphFormat().setAlignment(ParagraphAlignment.LEFT);
    }

    /**
     * 添加试卷信息
     */
    private void addPaperInfo(DocumentBuilder builder, QhExamPaper paper,
                             QhPaperExportDTO.FormatOptions formatOptions) throws Exception {
        builder.writeln("试卷信息：");

        if (StringUtils.isNotEmpty(paper.getSubject())) {
            builder.write("科目：" + paper.getSubject() + "    ");
        }
        if (StringUtils.isNotEmpty(paper.getGrade())) {
            builder.write("年级：" + paper.getGrade() + "    ");
        }
        if (StringUtils.isNotEmpty(paper.getRegion())) {
            builder.write("地区：" + paper.getRegion() + "    ");
        }
        if (StringUtils.isNotEmpty(paper.getPyear())) {
            builder.write("年份：" + paper.getPyear());
        }

        builder.writeln();

        if (StringUtils.isNotEmpty(paper.getNum())) {
            builder.write("题目数量：" + paper.getNum() + "题    ");
        }
        if (StringUtils.isNotEmpty(paper.getScore())) {
            builder.write("总分：" + paper.getScore() + "分");
        }

        builder.writeln();
        builder.writeln();

        // 添加分隔线
        builder.insertHorizontalRule();
        builder.writeln();
    }

    /**
     * 添加题目
     */
    private void addQuestions(DocumentBuilder builder, List<QhQuestionBank> questions,
                             QhPaperExportDTO exportDTO) throws Exception {
        QhPaperExportDTO.ContentOptions contentOptions = exportDTO.getContentOptions();
        QhPaperExportDTO.FormatOptions formatOptions = exportDTO.getFormatOptions();

        for (int i = 0; i < questions.size(); i++) {
            QhQuestionBank question = questions.get(i);

            // 添加题目序号
            if (formatOptions.getShowQuestionNumbers()) {
                builder.getFont().setBold(true);
                builder.write((i + 1) + ". ");
                builder.getFont().setBold(false);
            }

            // 添加题目内容
            addQuestionContent(builder, question);

            // 添加分数
            if (formatOptions.getShowScores() && StringUtils.isNotEmpty(question.getScore())) {
                builder.write("（" + question.getScore() + "分）");
            }

            builder.writeln();
            builder.writeln();

            // 添加答案
            if (contentOptions.getIncludeAnswers() && StringUtils.isNotEmpty(question.getQuestionAnswer())) {
                builder.getFont().setBold(true);
                builder.write("答案：");
                builder.getFont().setBold(false);
                addImageOrText(builder, question.getQuestionAnswer());
                builder.writeln();
            }

            // 添加解析
            if (contentOptions.getIncludeAnalysis() && StringUtils.isNotEmpty(question.getQuestionAnalyze())) {
                builder.getFont().setBold(true);
                builder.write("解析：");
                builder.getFont().setBold(false);
                addImageOrText(builder, question.getQuestionAnalyze());
                builder.writeln();
            }

            builder.writeln();
        }
    }

    /**
     * 添加题目内容（图片或文本）
     */
    private void addQuestionContent(DocumentBuilder builder, QhQuestionBank question) throws Exception {
        if (StringUtils.isNotEmpty(question.getContext())) {
            addImageOrText(builder, question.getContext());
        } else if (StringUtils.isNotEmpty(question.getOcrText())) {
            builder.write(question.getOcrText());
        }
    }

    /**
     * 添加图片或文本内容
     */
    private void addImageOrText(DocumentBuilder builder, String content) throws Exception {
        if (StringUtils.isEmpty(content)) {
            return;
        }

        // 判断是否为图片URL
        if (content.startsWith("http")) {
            try {
                // 下载图片并插入
                insertImageFromUrl(builder, content);
            } catch (Exception e) {
                log.warn("插入图片失败，使用文本替代: {}", e.getMessage());
                builder.write("[图片加载失败]");
            }
        } else {
            // 直接插入文本
            builder.write(content);
        }
    }

    /**
     * 从URL插入图片
     */
    private void insertImageFromUrl(DocumentBuilder builder, String imageUrl) throws Exception {
        try (InputStream imageStream = new URL(imageUrl).openStream()) {
            // 插入图片，设置合适的大小
            Shape shape = builder.insertImage(imageStream);

            // 设置图片大小（最大宽度为页面宽度的80%）
            double pageWidth = builder.getPageSetup().getPageWidth() -
                              builder.getPageSetup().getLeftMargin() -
                              builder.getPageSetup().getRightMargin();
            double maxWidth = pageWidth * 0.8;

            if (shape.getWidth() > maxWidth) {
                double ratio = maxWidth / shape.getWidth();
                shape.setWidth(maxWidth);
                shape.setHeight(shape.getHeight() * ratio);
            }
        }
    }

    /**
     * 转换为PDF并输出
     */
    private void convertToPdfAndOutput(Document doc, String paperName, HttpServletResponse response)
            throws Exception {
        // 设置响应头
        response.setContentType("application/pdf");
        String fileName = paperName + ".pdf";
        String encodedFileName = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-Disposition", "attachment; filename=" + encodedFileName);

        // 转换为PDF并输出
        doc.save(response.getOutputStream(), SaveFormat.PDF);
    }

    /**
     * 输出Word文档
     */
    private void outputWordDocument(Document doc, String paperName, HttpServletResponse response)
            throws Exception {
        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        String fileName = paperName + ".docx";
        String encodedFileName = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-Disposition", "attachment; filename=" + encodedFileName);

        // 输出Word文档
        doc.save(response.getOutputStream(), SaveFormat.DOCX);
    }

}
