# 试卷导出功能说明

## 功能概述

试卷导出功能支持将试卷导出为PDF或Word格式，可以灵活选择导出内容（试卷、答案、解析）和格式选项。采用同步导出方式，直接下载生成的文件。

## 主要特性

- ✅ 支持PDF和Word两种格式导出
- ✅ 可选择导出内容：试卷题目、答案、解析（支持任意组合）
- ✅ 丰富的格式选项：页面大小、字体大小、页边距等
- ✅ 同步导出：立即生成并下载文件
- ✅ 支持图片内容的处理和插入
- ✅ 简单易用的API接口

## API接口

### 1. 同步导出PDF
```
POST /qh/paper/export/pdf
Content-Type: application/json

{
  "paperId": "试卷ID",
  "exportType": "PDF",
  "contentOptions": {
    "includeQuestions": true,
    "includeAnswers": false,
    "includeAnalysis": false
  },
  "formatOptions": {
    "pageSize": "A4",
    "fontSize": 12,
    "showQuestionNumbers": true,
    "showScores": true,
    "margin": 20,
    "lineSpacing": 1.5
  },
  "flag": "ZJ"
}
```

### 2. 同步导出Word
```
POST /qh/paper/export/word
Content-Type: application/json

{
  "paperId": "试卷ID",
  "exportType": "WORD",
  "contentOptions": {
    "includeQuestions": true,
    "includeAnswers": true,
    "includeAnalysis": true
  },
  "formatOptions": {
    "pageSize": "A4",
    "fontSize": 12,
    "showQuestionNumbers": true,
    "showScores": true,
    "margin": 20,
    "lineSpacing": 1.5
  },
  "flag": "ZJ"
}
```



## 参数说明

### QhPaperExportDTO

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| paperId | String | 是 | 试卷ID |
| exportType | String | 是 | 导出类型：PDF、WORD |
| contentOptions | ContentOptions | 是 | 内容选项 |
| formatOptions | FormatOptions | 否 | 格式选项 |
| flag | String | 否 | 试卷标识：ZJ-组卷，其他-原生试卷 |

### ContentOptions

| 字段 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| includeQuestions | Boolean | true | 是否包含试卷题目 |
| includeAnswers | Boolean | false | 是否包含答案 |
| includeAnalysis | Boolean | false | 是否包含解析 |

### FormatOptions

| 字段 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| pageSize | String | A4 | 页面大小：A4、A3、Letter |
| fontSize | Integer | 12 | 字体大小 |
| showQuestionNumbers | Boolean | true | 是否显示题目序号 |
| showScores | Boolean | true | 是否显示分数 |
| margin | Integer | 20 | 页边距（毫米） |
| lineSpacing | Double | 1.5 | 行间距 |

## 前端使用示例

### 1. JavaScript原生调用

```javascript
// 导出PDF（仅试卷）
exportPaperToPdf('paper123', {
    includeQuestions: true,
    includeAnswers: false,
    includeAnalysis: false,
    fontSize: 14
});

// 导出Word（试卷+答案+解析）
exportPaperToWord('paper123', {
    includeQuestions: true,
    includeAnswers: true,
    includeAnalysis: true,
    fontSize: 12,
    pageSize: 'A4'
});
```

### 2. Vue组件使用

```vue
<template>
  <div>
    <el-button @click="showExportDialog = true">导出试卷</el-button>
    
    <PaperExportDialog
      :visible.sync="showExportDialog"
      :paper-id="currentPaperId"
      :paper-flag="currentPaperFlag"
    />
  </div>
</template>

<script>
import PaperExportDialog from './PaperExportDialog.vue';

export default {
  components: {
    PaperExportDialog
  },
  data() {
    return {
      showExportDialog: false,
      currentPaperId: 'paper123',
      currentPaperFlag: 'ZJ'
    };
  }
};
</script>
```

## 配置说明

### 权限配置

需要配置以下权限：
- `qh:paper:export` - 试卷导出权限

## 技术实现

### 后端技术栈
- Spring Boot + Aspose Words
- MinIO文件存储
- 同步文档生成

### 核心流程
1. 接收导出请求参数
2. 获取试卷和题目数据
3. 从MinIO下载图片资源
4. 使用Aspose Words生成文档
5. 转换为目标格式并直接输出到响应流

## 注意事项

1. **图片处理**：确保MinIO中的图片可正常访问
2. **内存使用**：大量图片可能占用较多内存
3. **权限控制**：确保用户有相应的导出权限
4. **网络超时**：大文件导出可能需要较长时间，注意前端超时设置

## 故障排查

### 常见问题

1. **导出失败**
   - 检查试卷ID是否存在
   - 检查MinIO连接是否正常
   - 查看后端日志错误信息

2. **图片显示异常**
   - 检查图片URL是否可访问
   - 确认网络连接正常
   - 查看Aspose Words许可证是否有效

3. **导出超时**
   - 检查网络连接稳定性
   - 确认服务器性能
   - 查看后端处理日志

### 日志查看

```bash
# 查看导出相关日志
tail -f logs/domino_log.log | grep "PaperExport"
```

## 扩展功能

后续可以考虑添加的功能：
- 批量导出多个试卷
- 自定义模板支持
- 水印添加
- 导出历史记录
- 异步导出（适用于大文件）
- 邮件发送导出文件
