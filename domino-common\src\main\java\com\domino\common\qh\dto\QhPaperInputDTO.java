package com.domino.common.qh.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * 客户端试卷DTO
 *
 */
@Data
public class QhPaperInputDTO implements Serializable {
    private static final long serialVersionUID = -5137566696030630605L;

    private String paperId;

    @NotBlank(message = "试卷名称不能为空")
    private String paperName;  //七年级代数专题卷

    @NotBlank(message = "试卷类型不能为空")
    private String paperType; //专题卷

    @NotBlank(message = "试卷来源不能为空")
    private String source;  //XX教育出版社

    @NotBlank(message = "所属地区不能为空")
    private String region;  //江苏

    private String libraryId;

    private String createTime;  //2023-10-10 09:00:00

    private List<QhQuestionInputDTO > questionInputDTOList;
}
