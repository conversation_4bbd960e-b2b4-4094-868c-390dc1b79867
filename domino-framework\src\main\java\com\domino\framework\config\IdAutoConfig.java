package com.domino.framework.config;

import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.baomidou.mybatisplus.core.incrementer.ImadcnIdentifierGenerator;
import com.domino.common.utils.spring.SpringUtils;
import com.imadcn.framework.idworker.algorithm.Snowflake;
import com.imadcn.framework.idworker.config.ZookeeperConfiguration;
import com.imadcn.framework.idworker.generator.SnowflakeGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;

import java.lang.reflect.Field;

@Slf4j
@Configuration
public class IdAutoConfig {
    // 从配置文件中获取连接Zookeeper服务器的列表. 包括IP地址和端口号. 多个地址用逗号分隔. 如: host1:2181,host2:2181
//    @Value("${zookeeper.connect-string:127.0.0.1:2181}")
//    private String zkServerLists;
//    @Value("${zookeeper.username}")
//    private String username;
//    @Value("${zookeeper.password}")
//    private String password;
//
//    @Bean
//    public IdentifierGenerator idGenerator() {
//        ZookeeperConfiguration zookeeperConfiguration = new ZookeeperConfiguration();
//        zookeeperConfiguration.setServerLists(zkServerLists);
//        zookeeperConfiguration.setDigest((username + ":" + password));
//        zookeeperConfiguration.setMaxRetries(10);
//        zookeeperConfiguration.setConnectionTimeoutMilliseconds(3000);
//        return new ImadcnIdentifierGenerator(zookeeperConfiguration);
//    }
//
//    @EventListener(ApplicationReadyEvent.class)
//    public void onApplicationReady() {
//        IdentifierGenerator bean = SpringUtils.getBean(IdentifierGenerator.class);
//
//        // 因为其私有属性未提供访问方法，只能通过反射查看workerId的值
//        try {
//            Class<ImadcnIdentifierGenerator> aClass = ImadcnIdentifierGenerator.class;
//            Field idGeneratorField = aClass.getDeclaredField("idGenerator");
//            idGeneratorField.setAccessible(true);
//            SnowflakeGenerator snowflakeGenerator = (SnowflakeGenerator) idGeneratorField.get(bean);
//
//            Class<? extends SnowflakeGenerator> snowflakeGeneratorClass = snowflakeGenerator.getClass();
//            Field snowflakeField = snowflakeGeneratorClass.getDeclaredField("snowflake");
//            snowflakeField.setAccessible(true);
//            Snowflake snowflake = (Snowflake) snowflakeField.get(snowflakeGenerator);
//
//            Class<? extends Snowflake> snowflakeClass = snowflake.getClass();
//            Field workerIdField = snowflakeClass.getDeclaredField("workerId");
//            workerIdField.setAccessible(true);
//            Long workerId = (Long) workerIdField.get(snowflake);
//
//            System.out.println("本节点的workerId是:" + workerId);
//        } catch (NoSuchFieldException | IllegalAccessException e) {
//            throw new RuntimeException(e);
//        }
//    }
}
