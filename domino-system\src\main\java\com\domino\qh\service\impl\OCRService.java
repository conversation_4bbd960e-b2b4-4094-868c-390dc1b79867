package com.domino.qh.service.impl;

import com.domino.common.qh.domain.OCRTextLine;
import com.domino.common.qh.dto.OcrResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

@Service
@Slf4j
public class OCRService {

    // 这里使用阿里云OCR为例，您需要替换为实际的OCR服务
    @Value("${aliyun.ocr.accessKeyId}")
    private String accessKeyId;

    @Value("${aliyun.ocr.accessKeySecret}")
    private String accessKeySecret;

    public OcrResult recognizePage(BufferedImage image) throws Exception {
        // 将图片转换为Base64
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(image, "png", baos);
        String base64Image = Base64.getEncoder().encodeToString(baos.toByteArray());

        // 这里应该调用实际的OCR API
        // 为了示例，我们模拟返回结果
        OcrResult result = new OcrResult();

        // 模拟OCR识别结果
        // 实际应该调用阿里云OCR API并解析返回的JSON
        result.setTextLines(mockOCRRecognition(image));

        return result;
    }

    private List<OCRTextLine> mockOCRRecognition(BufferedImage image) {
        // 这里应该是实际的OCR识别逻辑
        // 为了示例，返回模拟数据
        List<OCRTextLine> lines = new ArrayList<>();

        // 模拟题目文本行
        lines.add(new OCRTextLine("1. (5分) 下列关于Java的说法正确的是：", 50, 100, image.getWidth() - 100, 30));
        lines.add(new OCRTextLine("A. Java是一种编译型语言", 50, 140, 300, 25));
        lines.add(new OCRTextLine("B. Java支持多重继承", 50, 170, 300, 25));
        lines.add(new OCRTextLine("C. Java是跨平台的", 50, 200, 300, 25));
        lines.add(new OCRTextLine("D. Java不支持多线程", 50, 230, 300, 25));
        lines.add(new OCRTextLine("答案：C", 50, 270, 100, 25));
        lines.add(new OCRTextLine("解析：Java是一种跨平台的编程语言，通过JVM实现一次编写，到处运行。", 50, 300, 600, 25));

        return lines;
    }
}
