<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>domino</groupId>
        <artifactId>domino-project</artifactId>
        <version>3.8.7</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>
    <artifactId>domino-admin</artifactId>

    <description>
        web服务入口
    </description>

    <dependencies>
        <!-- swagger3-->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-boot-starter</artifactId>
        </dependency>

        <!-- 防止进入swagger页面报类型转换错误，排除3.0.0中的引用，手动增加1.6.2版本 -->
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-models</artifactId>
            <version>1.6.2</version>
        </dependency>

        <!-- Mysql驱动包 -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <!-- 核心模块-->
        <dependency>
            <groupId>domino</groupId>
            <artifactId>domino-framework</artifactId>
        </dependency>

        <!-- 定时任务-->
        <dependency>
            <groupId>domino</groupId>
            <artifactId>domino-quartz</artifactId>
        </dependency>

        <!-- 代码生成-->
        <dependency>
            <groupId>domino</groupId>
            <artifactId>domino-generator</artifactId>
        </dependency>

        <!-- Flink 核心依赖（已包含 flink-core） -->
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-java</artifactId>
            <version>1.15.0</version>
        </dependency>

        <!-- Flink 流处理核心依赖 -->
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-streaming-java</artifactId>
            <version>1.15.0</version>
        </dependency>

        <!-- Flink 客户端依赖（提交和执行任务必需） -->
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-clients</artifactId>
            <version>1.15.0</version>
        </dependency>

        <!-- Flink CDC 连接器（MySQL CDC核心依赖） -->
        <dependency>
            <groupId>com.ververica</groupId>
            <artifactId>flink-connector-mysql-cdc</artifactId>
            <version>2.4.0</version>
        </dependency>

        <!-- Flink 连接器基础依赖（CDC依赖的基础组件） -->
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-connector-base</artifactId>
            <version>1.15.0</version>
        </dependency>

        <!-- （可选）如果不使用Flink Table API，可移除以下两个依赖 -->
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-table-common</artifactId>
            <version>1.15.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-table-api-java-bridge</artifactId>
            <version>1.15.0</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- Spring Boot 插件（推荐方式） -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.5.15</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                        <configuration>
                            <mainClass>com.domino.DominoApplication</mainClass>
                            <finalName>${project.artifactId}</finalName>
                            <!-- 正确的 excludes 写法 -->
                            <excludes>
                                <exclude>
                                    <groupId>org.slf4j</groupId>
                                    <artifactId>*</artifactId>
                                </exclude>
                                <exclude>
                                    <groupId>log4j</groupId>
                                    <artifactId>*</artifactId>
                                </exclude>
                            </excludes>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
