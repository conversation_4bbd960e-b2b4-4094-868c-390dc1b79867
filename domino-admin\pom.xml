<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>domino</groupId>
        <artifactId>domino-project</artifactId>
        <version>3.8.7</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>
    <artifactId>domino-admin</artifactId>

    <description>
        web服务入口
    </description>

    <dependencies>
        <!-- swagger3-->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-boot-starter</artifactId>
        </dependency>

        <!-- 防止进入swagger页面报类型转换错误，排除3.0.0中的引用，手动增加1.6.2版本 -->
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-models</artifactId>
            <version>1.6.2</version>
        </dependency>

        <!-- Mysql驱动包 -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <!-- 核心模块-->
        <dependency>
            <groupId>domino</groupId>
            <artifactId>domino-framework</artifactId>
        </dependency>

        <!-- 定时任务-->
        <dependency>
            <groupId>domino</groupId>
            <artifactId>domino-quartz</artifactId>
        </dependency>

        <!-- 代码生成-->
        <dependency>
            <groupId>domino</groupId>
            <artifactId>domino-generator</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.5.15</version>
                <configuration>
                    <!-- 指定该jar包启动时的主类[建议] -->
                    <mainClass>com.domino.DominoApplication</mainClass>
                    <!-- 设置最终JAR包的名称，不包含版本号 -->
                    <finalName>${project.artifactId}</finalName>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <!-- 可以打成可执行的运行包（.jar/.war） -->
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
