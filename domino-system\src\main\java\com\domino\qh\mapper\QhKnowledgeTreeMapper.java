package com.domino.qh.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.domino.common.qh.domain.QhKnowledgeTree;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 内容管理 数据层
 */
@Mapper
public interface QhKnowledgeTreeMapper extends BaseMapper<QhKnowledgeTree> {
    /**
     * 查询内容管理数据
     *
     * @param knowledge 内容信息
     * @return 内容信息集合
     */
    public List<QhKnowledgeTree> selectKnowledgeList(@Param("knowledge") QhKnowledgeTree knowledge, @Param("createBy") String createBy, @Param("currentUser") String currentUser);

    /**
     * 首页数据查询
     */
    Integer selectCount(@Param("beginTime") String beginTime, @Param("endTime") String endTime);

    /**
     * 根据内容ID查询信息
     *
     * @param id 内容ID
     * @return 内容信息
     */
    public QhKnowledgeTree selectKnowledgeById(String id);

    /**
     * 根据内容ID查询信息
     *
     * @param ids 内容ID
     * @return 内容信息
     */
    public List<QhKnowledgeTree> selectKnowledgeByIds(@Param("ids") List<String> ids);

    /**
     * 根据ID查询所有子内容
     *
     * @param id 内容ID
     * @return 内容列表
     */
    public List<QhKnowledgeTree> selectChildrenKnowledgeById(String id);

    /**
     * 根据ID查询所有子内容（正常状态）
     *
     * @param id 内容ID
     * @return 子内容数
     */
    public int selectNormalChildrenKnowledgeById(String id);

    /**
     * 是否存在子节点
     *
     * @param id 内容ID
     * @return 结果
     */
    public int hasChildById(String id);

    /**
     * 校验内容名称是否唯一
     *
     * @param name     内容名称
     * @param parentId 父内容ID
     * @return 结果
     */
    public QhKnowledgeTree checkKnowledgeNameUnique(@Param("name") String name, @Param("parentId") String parentId);

    /**
     * 新增内容信息
     *
     * @param Knowledge 内容信息
     * @return 结果
     */
    public int insertKnowledge(QhKnowledgeTree Knowledge);

    /**
     * 修改内容信息
     *
     * @param Knowledge 内容信息
     * @return 结果
     */
    public int updateKnowledge(QhKnowledgeTree Knowledge);

    /**
     * 修改所在内容正常状态
     *
     * @param ids 内容ID组
     */
    public void updateKnowledgeStatusNormal(String[] ids);

    /**
     * 修改子元素关系
     *
     * @param knowledgeTree 子元素
     * @return 结果
     */
    public int updateKnowledgeChildren(@Param("knowledgeTree") List<QhKnowledgeTree> knowledgeTree);

    /**
     * 删除内容管理信息
     *
     * @param id 内容ID
     * @return 结果
     */
    public int deleteKnowledgeById(String id);

    List<String> getKnowledgeIdsByChapterIds(List<String> chapterIds);
}
