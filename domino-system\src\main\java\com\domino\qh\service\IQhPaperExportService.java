package com.domino.qh.service;

import com.domino.common.qh.dto.QhPaperExportDTO;

import javax.servlet.http.HttpServletResponse;

/**
 * 试卷导出服务接口
 *
 * <AUTHOR>
 * @date 2025-08-11
 */
public interface IQhPaperExportService {

    /**
     * 导出试卷为PDF
     *
     * @param exportDTO 导出参数
     * @param response  HTTP响应
     */
    void exportToPdf(QhPaperExportDTO exportDTO, HttpServletResponse response);

    /**
     * 导出试卷为Word
     *
     * @param exportDTO 导出参数
     * @param response  HTTP响应
     */
    void exportToWord(QhPaperExportDTO exportDTO, HttpServletResponse response);
}
