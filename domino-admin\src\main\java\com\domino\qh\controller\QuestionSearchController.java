package com.domino.qh.controller;

import com.domino.common.core.controller.BaseController;
import com.domino.common.core.domain.AjaxResult;
import com.domino.common.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import com.domino.common.elasticsearch.ElasticSearchMappingConfig;
import com.domino.common.elasticsearch.ElasticSearchMappingLoader;
import com.domino.common.event.QuestionEventPublisher;
import com.domino.common.qh.document.QuestionDocument;
import com.domino.common.qh.form.QuestionSearchForm;
import com.domino.common.qh.document.QuestionSearchBuilder;

import com.domino.qh.service.QuestionElasticSearchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 题目搜索控制器
 */
@Api(tags = "题目搜索管理")
@Slf4j
@RestController
@RequestMapping("/qh/question/search")
public class QuestionSearchController extends BaseController {

    @Resource
    private QuestionElasticSearchService questionElasticSearchService;

    @Resource
    private ElasticSearchMappingLoader mappingLoader;

    @Resource
    private ElasticSearchMappingConfig mappingConfig;

    @Resource
    private QuestionEventPublisher questionEventPublisher;

    /**
     * 创建题目索引
     */
    @ApiOperation("创建题目索引")
    @PostMapping("/createIndex")
    public AjaxResult createIndex() {
        try {
            boolean result = questionElasticSearchService.createQuestionIndex();
            if (result) {
                return success("题目索引创建成功");
            } else {
                return error("题目索引创建失败，可能索引已存在");
            }
        } catch (Exception e) {
            return error("创建题目索引失败：" + e.getMessage());
        }
    }

    /**
     * 检查题目索引是否存在
     */
    @ApiOperation("检查题目索引是否存在")
    @GetMapping("/indexExists")
    public AjaxResult indexExists() {
        try {
            boolean exists = questionElasticSearchService.isQuestionIndexExist();
            return success(exists ? "题目索引已存在" : "题目索引不存在");
        } catch (Exception e) {
            return error("检查题目索引失败：" + e.getMessage());
        }
    }

    /**
     * 删除题目索引
     */
    @ApiOperation("删除题目索引")
    @DeleteMapping("/deleteIndex")
    public AjaxResult deleteQuestionIndex() {
        try {
            boolean deleted = questionElasticSearchService.deleteQuestionIndex();
            if (deleted) {
                return success("题目索引删除成功");
            } else {
                return error("题目索引删除失败");
            }
        } catch (Exception e) {
            return error("删除题目索引失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询题目
     */
    @ApiOperation("根据ID查询题目")
    @GetMapping("/{questionId}")
    public AjaxResult getQuestionById(@ApiParam(value = "题目ID", required = true) @PathVariable String questionId) {
        try {
            QuestionDocument question = questionElasticSearchService.getQuestionById(questionId);
            if (question != null) {
                return success(question);
            } else {
                return error("题目不存在");
            }
        } catch (Exception e) {
            return error("查询题目失败：" + e.getMessage());
        }
    }

    /**
     * 根据试卷名称删除所有题目
     */
    @ApiOperation("根据试卷名称删除所有题目")
    @DeleteMapping("/paper/{paperName}")
    public AjaxResult deleteQuestionsByPaperName(
            @ApiParam(value = "试卷名称", required = true) @PathVariable String paperName) {
        try {
            questionElasticSearchService.deleteQuestionsByPaperName(paperName);
            return success("删除成功");
        } catch (Exception e) {
            return error("删除试卷题目失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID删除题目
     */
    @ApiOperation("根据ID删除题目")
    @DeleteMapping("/{questionId}")
    public AjaxResult deleteQuestionById(@ApiParam(value = "题目ID", required = true) @PathVariable String questionId) {
        try {
            questionElasticSearchService.deleteQuestionById(questionId);
            return success("删除成功");
        } catch (Exception e) {
            return error("删除题目失败：" + e.getMessage());
        }
    }

    /**
     * 保存题目到ES
     */
    @ApiOperation("保存题目到ES")
    @PostMapping("/save")
    public AjaxResult saveQuestion(@RequestBody QuestionDocument questionDocument) {
        try {
            questionElasticSearchService.saveQuestion(questionDocument);
            return success("保存成功");
        } catch (Exception e) {
            return error("保存题目失败：" + e.getMessage());
        }
    }

    /**
     * 更新题目信息
     */
    @ApiOperation("更新题目信息")
    @PutMapping("/update")
    public AjaxResult updateQuestion(@RequestBody QuestionDocument questionDocument) {
        try {
            if (questionDocument.getId() == null) {
                return error("题目ID不能为空");
            }
            questionElasticSearchService.updateQuestion(questionDocument);
            return success("更新成功");
        } catch (Exception e) {
            return error("更新题目失败：" + e.getMessage());
        }
    }

    /**
     * 搜索题目
     */
    @ApiOperation("搜索题目")
    @PostMapping("/search")
    public AjaxResult searchQuestions(@RequestBody QuestionSearchForm searchForm) {
        try {
            Map<String, Object> result = questionElasticSearchService.searchQuestions(searchForm);
            return success(result);
        } catch (Exception e) {
            return error("搜索题目失败：" + e.getMessage());
        }
    }

    /**
     * 高级搜索题目（支持多条件组合查询）
     */
    @ApiOperation("高级搜索题目")
    @PostMapping("/advancedSearch")
    public AjaxResult advancedSearchQuestions(@RequestBody QuestionSearchForm searchForm) {
        try {
            // 验证搜索参数
            if (searchForm.getPageNum() == null || searchForm.getPageNum() < 1) {
                searchForm.setPageNum(1);
            }
            if (searchForm.getPageSize() == null || searchForm.getPageSize() < 1 || searchForm.getPageSize() > 100) {
                searchForm.setPageSize(10);
            }
            Map<String, Object> result = questionElasticSearchService.searchQuestions(searchForm);
            // 添加搜索统计信息
            Map<String, Object> searchInfo = new HashMap<>();
            searchInfo.put("searchConditions", buildSearchConditionsSummary(searchForm));
            searchInfo.put("totalHits", result.get("total"));
            searchInfo.put("pageNum", searchForm.getPageNum());
            searchInfo.put("pageSize", searchForm.getPageSize());
            result.put("searchInfo", searchInfo);
            return success(result);
        } catch (Exception e) {
            return error("高级搜索题目失败：" + e.getMessage());
        }
    }

    /**
     * 构建搜索条件摘要
     */
    private Map<String, Object> buildSearchConditionsSummary(QuestionSearchForm searchForm) {
        Map<String, Object> summary = new HashMap<>();

        if (StringUtils.hasText(searchForm.getKeyword())) {
            summary.put("关键词", searchForm.getKeyword());
        }
        if (!CollectionUtils.isEmpty(searchForm.getQuestionTypes())) {
            summary.put("题目类型", searchForm.getQuestionTypes());
        }
        if (!CollectionUtils.isEmpty(searchForm.getDifficulties())) {
            summary.put("难度", searchForm.getDifficulties());
        }
        if (!CollectionUtils.isEmpty(searchForm.getPaperTypes())) {
            summary.put("试卷类型", searchForm.getPaperTypes());
        }
        if (!CollectionUtils.isEmpty(searchForm.getYears())) {
            summary.put("年份", searchForm.getYears());
        }
        if (!CollectionUtils.isEmpty(searchForm.getRegions())) {
            summary.put("地区", searchForm.getRegions());
        }
        if (!CollectionUtils.isEmpty(searchForm.getPaperSources())) {
            summary.put("试卷来源", searchForm.getPaperSources());
        }
        if (!CollectionUtils.isEmpty(searchForm.getTags())) {
            summary.put("试题标签", searchForm.getTags());
        }
        if (StringUtils.hasText(searchForm.getKnowledgeKeyword())) {
            summary.put("知识点关键词", searchForm.getKnowledgeKeyword());
        }
        if (!CollectionUtils.isEmpty(searchForm.getKnowledgeTreeIds())) {
            summary.put("知识点数量", searchForm.getKnowledgeTreeIds().size());
        }

        return summary;
    }

    /**
     * 获取当前映射配置
     */
    @ApiOperation("获取当前映射配置")
    @GetMapping("/mapping/current")
    public AjaxResult getCurrentMapping() {
        try {
            String mapping = mappingLoader.getQuestionMapping();
            Map<String, Object> result = new HashMap<>();
            result.put("enabled", mappingLoader.isQuestionMappingEnabled());
            result.put("indexName", mappingLoader.getQuestionIndexName());
            result.put("mapping", mapping);
            return success(result);
        } catch (Exception e) {
            return error("获取映射配置失败：" + e.getMessage());
        }
    }

    /**
     * 重新加载映射配置
     */
    @ApiOperation("重新加载映射配置")
    @PostMapping("/mapping/reload")
    public AjaxResult reloadMapping() {
        try {
            mappingLoader.reloadMapping();
            return success("重新加载成功");
        } catch (Exception e) {
            return error("重新加载映射配置失败：" + e.getMessage());
        }
    }

    /**
     * 获取映射配置详情
     */
    @ApiOperation("获取映射配置详情")
    @GetMapping("/mapping/config")
    public AjaxResult getMappingConfig() {
        try {
            return success(mappingConfig.getQuestion());
        } catch (Exception e) {
            return error("获取映射配置详情失败：" + e.getMessage());
        }
    }

    /**
     * 重建ES索引（通过事件）
     */
    @ApiOperation("重建ES索引")
    @PostMapping("/rebuildIndex")
    public AjaxResult rebuildIndex() {
        try {
            questionEventPublisher.publishRebuildIndexEvent();
            return success("重建索引事件已发布，请稍后查看执行结果");
        } catch (Exception e) {
            return error("发布重建索引事件失败：" + e.getMessage());
        }
    }

    /**
     * 同步数据到ES（通过事件）
     */
    @ApiOperation("同步数据到ES")
    @PostMapping("/syncData")
    public AjaxResult syncData(@RequestParam(required = false) String extraParam) {
        try {
            questionEventPublisher.publishSyncDataEvent(extraParam);
            return success("同步数据事件已发布，请稍后查看执行结果");
        } catch (Exception e) {
            return error("发布同步数据事件失败：" + e.getMessage());
        }
    }

    /**
     * 便捷搜索题目（使用QuestionSearchBuilder）
     */
    @ApiOperation("便捷搜索题目")
    @GetMapping("/quickSearch")
    public AjaxResult quickSearchQuestions(
            @ApiParam("关键词") @RequestParam(required = false) String keyword,
            @ApiParam("题目类型") @RequestParam(required = false) String questionType,
            @ApiParam("难度") @RequestParam(required = false) String difficulty,
            @ApiParam("年份") @RequestParam(required = false) String year,
            @ApiParam("地区") @RequestParam(required = false) String region,
            @ApiParam("试卷类型") @RequestParam(required = false) String paperType,
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") Integer pageSize) {
        try {
            // 使用QuestionSearchBuilder构建搜索条件
            QuestionSearchForm searchForm = QuestionSearchBuilder.builder()
                .keyword(keyword).questionType(questionType).difficulty(difficulty)
                .year(year).region(region).paperType(paperType).page(pageNum, pageSize)
                .sort("createTime", "desc").build();

            Map<String, Object> result = questionElasticSearchService.searchQuestions(searchForm);
            // 构建搜索条件摘要
            Map<String, Object> searchSummary = buildQuickSearchSummary(keyword, questionType, difficulty, year, region, paperType);
            result.put("searchConditions", searchSummary);
            return success(result);
        } catch (Exception e) {
            return error("搜索失败：" + e.getMessage());
        }
    }

    /**
     * 构建便捷搜索条件摘要
     */
    private Map<String, Object> buildQuickSearchSummary(String keyword, String questionType,
                                                       String difficulty, String year,
                                                       String region, String paperType) {
        Map<String, Object> summary = new HashMap<>();
        if (StringUtils.hasText(keyword)) {
            summary.put("关键词", keyword);
        }
        if (StringUtils.hasText(questionType)) {
            summary.put("题目类型", questionType);
        }
        if (StringUtils.hasText(difficulty)) {
            summary.put("难度", difficulty);
        }
        if (StringUtils.hasText(year)) {
            summary.put("年份", year);
        }
        if (StringUtils.hasText(region)) {
            summary.put("地区", region);
        }
        if (StringUtils.hasText(paperType)) {
            summary.put("试卷类型", paperType);
        }
        return summary;
    }

}
