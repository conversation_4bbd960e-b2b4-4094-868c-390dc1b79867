package com.domino.qh.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.domino.common.annotation.DataScope;
import com.domino.common.constant.QhConstants;
import com.domino.common.constant.UserConstants;
import com.domino.common.core.domain.TreeSelect;
import com.domino.common.exception.qh.QhException;
import com.domino.common.qh.domain.QhKnowledgeTree;
import com.domino.common.utils.StringUtils;
import com.domino.common.utils.spring.SpringUtils;
import com.domino.qh.mapper.QhKnowledgeQuestionMapper;
import com.domino.qh.mapper.QhKnowledgeTreeMapper;
import com.domino.qh.service.IQhKnowledgeTreeService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.domino.common.utils.SecurityUtils.getUsername;

/**
 * 内容管理 服务实现
 */
@Service
public class QhKnowledgeTreeServiceImpl implements IQhKnowledgeTreeService {

    @Resource
    private QhKnowledgeTreeMapper knowledgeTreeMapper;

    @Resource
    private QhKnowledgeQuestionMapper knowledgeQuestionMapper;

    /**
     * 查询内容管理数据
     *
     * @param query 查询内容信息
     * @return 内容信息集合
     */
    @Override
    @DataScope(knowledgeAlias = "d")
    public List<QhKnowledgeTree> selectKnowledgeTreeList(QhKnowledgeTree query) {
        return knowledgeTreeMapper.selectKnowledgeList(query, query.getCreateBy(), getUsername());
    }

    @Override
    public Integer dashboard(String beginTime, String endTime) {
        // 查询总试题数
        return knowledgeTreeMapper.selectCount(beginTime, endTime);
    }

    /**
     * 查询内容树结构信息
     *
     * @param query 内容信息
     * @return 内容树信息集合
     */
    @Override
    public List<TreeSelect> buildKnowledgeTreeSelect(QhKnowledgeTree query) {
        // status: '0' 查询状态正常的节点，排除掉停用节点
        query.setStatus("0");
        List<QhKnowledgeTree> qhKnowledgeList = SpringUtils.getAopProxy(this).selectKnowledgeTreeList(query);
        return buildKnowledgeTreeSelect(qhKnowledgeList);
    }

    /**
     * 构建前端所需要树结构
     *
     * @param qhKnowledgeList 内容列表
     * @return 树结构列表
     */
    @Override
    public List<QhKnowledgeTree> buildKnowledgeTree(List<QhKnowledgeTree> qhKnowledgeList) {
        List<QhKnowledgeTree> returnList = new ArrayList<QhKnowledgeTree>();
        List<String> tempList = qhKnowledgeList.stream().map(QhKnowledgeTree::getId).collect(Collectors.toList());
        for (QhKnowledgeTree QhKnowledge : qhKnowledgeList) {
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(QhKnowledge.getParentId())) {
                recursionFn(qhKnowledgeList, QhKnowledge);
                returnList.add(QhKnowledge);
            }
        }
        if (returnList.isEmpty()) {
            returnList = qhKnowledgeList;
        }
        return returnList;
    }

    /**
     * 构建前端所需要下拉树结构
     *
     * @param qhKnowledgeList 内容列表
     * @return 下拉树结构列表
     */
    @Override
    public List<TreeSelect> buildKnowledgeTreeSelect(List<QhKnowledgeTree> qhKnowledgeList) {
        List<QhKnowledgeTree> QhKnowledgeTrees = buildKnowledgeTree(qhKnowledgeList);
        return QhKnowledgeTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    /**
     * 根据内容ID查询信息
     *
     * @param id 内容ID
     * @return 内容信息
     */
    @Override
    public QhKnowledgeTree selectKnowledgeById(String id) {
        return knowledgeTreeMapper.selectKnowledgeById(id);
    }

    /**
     * 是否存在子节点
     *
     * @param id 内容ID
     * @return 结果
     */
    @Override
    public boolean hasChildById(String id) {
        int result = knowledgeTreeMapper.hasChildById(id);
        return result > 0;
    }

    /**
     * 校验内容名称是否唯一
     *
     * @param qhKnowledge 内容信息
     * @return 结果
     */
    @Override
    public boolean checkKnowledgeNameUnique(QhKnowledgeTree qhKnowledge) {
        String id = StringUtils.isNull(qhKnowledge.getId()) ? "-1" : qhKnowledge.getId();
        QhKnowledgeTree info = knowledgeTreeMapper.checkKnowledgeNameUnique(qhKnowledge.getName(), qhKnowledge.getParentId());
        if (StringUtils.isNotNull(info) && !info.getId().equals(id)) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验内容是否有查询数据权限
     *
     * @param id 内容id
     */
    @Override
    public boolean checkQueryDataAllowed(String id) {
        // 除了admin用户以外，只允许修改或删除自己创建的题库
        QhKnowledgeTree qhKnowledgeTree = this.selectKnowledgeById(id);
        if (Objects.isNull(qhKnowledgeTree)) {
            throw new RuntimeException("内容不存在，请刷新后重试！");
        }
        return qhKnowledgeTree.isAccessibleQuery();
    }

    /**
     * 校验内容是否有更新数据权限
     *
     * @param id 内容id
     */
    @Override
    public boolean checkUpdateDataAllowed(String id) {
        // 除了admin用户以外，只允许修改或删除自己创建的题库
        QhKnowledgeTree qhKnowledgeTree = this.selectKnowledgeById(id);
        if (Objects.isNull(qhKnowledgeTree)) {
            throw new RuntimeException("内容不存在，请刷新后重试！");
        }
        return qhKnowledgeTree.isAccessibleUpdate();
    }

    /**
     * 新增保存内容信息
     *
     * @param addQhKnowledgeTree 新增内容信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertKnowledge(QhKnowledgeTree addQhKnowledgeTree) {
        // 如果不是树的顶层，则需要判断上级
        if (!"0".equals(addQhKnowledgeTree.getParentId())) {
            QhKnowledgeTree info = knowledgeTreeMapper.selectKnowledgeById(addQhKnowledgeTree.getParentId());
            // // 如果父节点不为正常状态,则不允许新增子节点
            // if (!QhConstants.KNOWLEDGE_NORMAL.equals(info.getStatus())) {
            //     throw new ServiceException("内容停用，不允许新增");
            // }
            // 判断父节点是否有操作权限
            if (!info.isAccessibleUpdate()) {
                throw new RuntimeException("您没有操作权限！");
            }

            // 避免加入null或空串
            String ancestors = StringUtils.hasText(info.getAncestors()) ? info.getAncestors() + "," + addQhKnowledgeTree.getParentId() : addQhKnowledgeTree.getParentId();
            addQhKnowledgeTree.setAncestors(ancestors);
            addQhKnowledgeTree.setShareType(info.getShareType());
        }
        addQhKnowledgeTree.setId(IdUtil.fastSimpleUUID());
        addQhKnowledgeTree.setDelFlag("0");

        // 是否要复制其他题库数据
        if (StringUtils.hasText(addQhKnowledgeTree.getCopyFromTopNode())) {
            // 校验当前创建的节点是否为顶层节点
            if (!"0".equals(addQhKnowledgeTree.getParentId())) {
                throw new RuntimeException("仅允许题库操作！");
            }

            QhKnowledgeTree copyFronQhKnowledgeTree = knowledgeTreeMapper.selectKnowledgeById(addQhKnowledgeTree.getCopyFromTopNode());
            if (!"0".equals(copyFronQhKnowledgeTree.getParentId())) {
                throw new RuntimeException("被复制节点必须是题库！");
            }

            if (!"1".equals(copyFronQhKnowledgeTree.getShareType()) && !getUsername().equals(copyFronQhKnowledgeTree.getCreateBy())) {
                throw new RuntimeException("对该题库无操作权限！");
            }

            // 获取被复制题库下的所有节点数据
            QhKnowledgeTree query = new QhKnowledgeTree();
            query.setAncestors(addQhKnowledgeTree.getCopyFromTopNode());
            List<QhKnowledgeTree> qhKnowledgeTreeList = knowledgeTreeMapper.selectKnowledgeList(query, "", getUsername());

            // 复制节点数据并重新入库
            qhKnowledgeTreeList.stream()
                    .filter(qhKnowledgeTree -> !"0".equals(qhKnowledgeTree.getParentId()))
                    .forEach(copyData -> {
                        copyData.setId(IdUtil.fastSimpleUUID());
                        if (addQhKnowledgeTree.getCopyFromTopNode().equals(copyData.getParentId())) {
                            copyData.setParentId(addQhKnowledgeTree.getId());
                        }

                        Set<String> ancestorIds = Arrays.stream(copyData.getAncestors().split(",")).collect(Collectors.toSet());
                        // 替换节点祖级列表中的id
                        String ancestorsStr = ancestorIds.stream().map(ancestorId -> {
                            if (ancestorId.equals(addQhKnowledgeTree.getCopyFromTopNode())) {
                                return addQhKnowledgeTree.getId();
                            }
                            return ancestorId;
                        }).collect(Collectors.joining(","));
                        copyData.setAncestors(ancestorsStr);
                        knowledgeTreeMapper.insertKnowledge(copyData);
                    });
        }

        return knowledgeTreeMapper.insertKnowledge(addQhKnowledgeTree);
    }

    /**
     * 修改保存内容信息
     *
     * @param qhKnowledge 内容信息
     * @return 结果
     */
    @Override
    public int updateKnowledge(QhKnowledgeTree qhKnowledge) {
        QhKnowledgeTree newParentQhKnowledge = knowledgeTreeMapper.selectKnowledgeById(qhKnowledge.getParentId());
        QhKnowledgeTree oldQhKnowledge = knowledgeTreeMapper.selectKnowledgeById(qhKnowledge.getId());
        if (StringUtils.isNotNull(newParentQhKnowledge) && StringUtils.isNotNull(oldQhKnowledge)) {
            String newAncestors = newParentQhKnowledge.getAncestors() + "," + newParentQhKnowledge.getId();
            String oldAncestors = oldQhKnowledge.getAncestors();
            qhKnowledge.setAncestors(newAncestors);
            updateKnowledgeChildren(qhKnowledge.getId(), newAncestors, oldAncestors);
        }
        int result = knowledgeTreeMapper.updateKnowledge(qhKnowledge);
        if (QhConstants.KNOWLEDGE_NORMAL.equals(qhKnowledge.getStatus()) && StringUtils.isNotEmpty(qhKnowledge.getAncestors())
                && !StringUtils.equals("0", qhKnowledge.getAncestors())) {
            // 如果该内容是启用状态，则启用该内容的所有上级内容
            updateParentKnowledgeStatusNormal(qhKnowledge);
        }
        return result;
    }

    /**
     * 修改该内容的父级内容状态
     *
     * @param qhKnowledge 当前内容
     */
    private void updateParentKnowledgeStatusNormal(QhKnowledgeTree qhKnowledge) {
        String ancestors = qhKnowledge.getAncestors();
        String[] ids = ancestors.split(",");
        knowledgeTreeMapper.updateKnowledgeStatusNormal(ids);
    }

    /**
     * 修改子元素关系
     *
     * @param id           被修改的内容ID
     * @param newAncestors 新的父ID集合
     * @param oldAncestors 旧的父ID集合
     */
    public void updateKnowledgeChildren(String id, String newAncestors, String oldAncestors) {
        List<QhKnowledgeTree> children = knowledgeTreeMapper.selectChildrenKnowledgeById(id);
        for (QhKnowledgeTree child : children) {
            child.setAncestors(child.getAncestors().replaceFirst(oldAncestors, newAncestors));
        }
        if (!children.isEmpty()) {
            knowledgeTreeMapper.updateKnowledgeChildren(children);
        }
    }

    /**
     * 删除内容管理信息
     *
     * @param id 内容ID
     * @return 结果
     */
    @Override
    // @DataScope(knowledgeAlias = "d", userAlias = "u")
    public int deleteKnowledgeById(String id) {
        // 删除knowledgeTree即可，无需删除knowledge_question与question
        return knowledgeTreeMapper.deleteKnowledgeById(id);
    }

    @Override
    public List<TreeSelect> buildKnowledgeTreeByNodeType(QhKnowledgeTree query) {
        if (StrUtil.isBlank(query.getNodeType()) && CollectionUtils.isEmpty(query.getAncestorIdList())) {
            throw new QhException("nodeType和ancestorIdList必须选传一个，请检查！");
        }
        query.setStatus("0");
        if (StrUtil.isNotBlank(query.getNodeType()) && StrUtil.equalsAny(query.getNodeType(), "1", "2")) {
            List<QhKnowledgeTree> qhKnowledgeList = SpringUtils.getAopProxy(this).selectKnowledgeTreeList(query);
            return buildKnowledgeTreeSelect(qhKnowledgeList);
        }
        // 如果选中了题库
        List<QhKnowledgeTree> qhKnowledgeList = SpringUtils.getAopProxy(this).selectKnowledgeTreeList(query);
        if (StrUtil.isNotBlank(query.getNodeType()) && StrUtil.equals(query.getNodeType(), "4")) {
            qhKnowledgeList = qhKnowledgeList.stream().filter(ObjectUtil::isNotNull).filter(item -> StrUtil.equals("4", query.getNodeType())).collect(Collectors.toList());
            return buildKnowledgeTreeSelect(qhKnowledgeList);
        }
        if (StrUtil.isNotBlank(query.getNodeType()) && StrUtil.equals(query.getNodeType(), "5")) {
            qhKnowledgeList = qhKnowledgeList.stream().filter(ObjectUtil::isNotNull).filter(item -> StrUtil.equals("5", query.getNodeType())).collect(Collectors.toList());
            return buildKnowledgeTreeSelect(qhKnowledgeList);
        }
        return Collections.emptyList();
    }

    @Override
    public List<String> queryLyByIds(List<String> questionBankIdList) {
        List<String> nameIds = new ArrayList<>();
        List<String> nameIdList = new ArrayList<>();
        //通过题目id查询知识点
        List<QhKnowledgeTree> knowledgeTreeList = new ArrayList<>();
        for (String questionBankId : questionBankIdList) {
            knowledgeTreeList.addAll(knowledgeQuestionMapper.selectKnowledgeTreesByQuestionId(questionBankId));
        }
        List<String> ancestorsList = knowledgeTreeList.stream().filter(Objects::nonNull).map(QhKnowledgeTree::getAncestors).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(ancestorsList)) {
            ancestorsList.forEach(it -> {
                String[] split = it.trim().split(",");
                Optional.of(split).ifPresent(sp -> {
                            if (sp.length > 0 && !nameIds.contains(split[0]) && StrUtil.isNotBlank(split[0])) {
                                nameIds.add(split[0]);
                            }
                        });
            });
        }
        if (CollectionUtils.isNotEmpty(nameIds)) {
            List<QhKnowledgeTree> knowledgeTreeList1 = knowledgeTreeMapper.selectKnowledgeByIds(nameIds);
            nameIdList = knowledgeTreeList1.stream().filter(Objects::nonNull).map(QhKnowledgeTree::getName).distinct().collect(Collectors.toList());
        }
        return nameIdList;
    }

    /**
     * 递归列表
     */
    private void recursionFn(List<QhKnowledgeTree> list, QhKnowledgeTree t) {
        // 得到子节点列表
        List<QhKnowledgeTree> childList = getChildList(list, t);
        t.setChildren(childList);
        for (QhKnowledgeTree tChild : childList) {
            if (hasChild(list, tChild)) {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<QhKnowledgeTree> getChildList(List<QhKnowledgeTree> list, QhKnowledgeTree t) {
        List<QhKnowledgeTree> tlist = new ArrayList<QhKnowledgeTree>();
        for (QhKnowledgeTree n : list) {
            if (StringUtils.isNotNull(n.getParentId()) && n.getParentId().equals(t.getId())) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<QhKnowledgeTree> list, QhKnowledgeTree t) {
        return !getChildList(list, t).isEmpty();
    }
}
