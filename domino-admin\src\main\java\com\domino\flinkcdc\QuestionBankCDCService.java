package com.domino.flinkcdc;

import com.ververica.cdc.connectors.mysql.source.MySqlSource;
import com.ververica.cdc.connectors.mysql.table.StartupOptions;
import com.ververica.cdc.debezium.JsonDebeziumDeserializationSchema;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 监控question_bank表的数据变化（新增、更新、删除）
 * 从Spring数据源配置读取数据库连接信息
 */
@Component
public class QuestionBankCDCService {

    // 从Spring数据源配置读取信息
    @Value("${spring.datasource.url}")
    private String jdbcUrl;
    @Value("${spring.datasource.username}")
    private String username;
    @Value("${spring.datasource.password}")
    private String password;
    // 可配置的其他CDC参数
    @Value("${mysql.cdc.server-id:101}")
    private String serverId;
    @Value("${mysql.cdc.table:qh.qh_question_bank}")
    private String table;

    /**
     * 应用启动时初始化并启动Flink CDC任务
     */
    @PostConstruct
    public void startMonitoring() {
        // 启动新线程执行Flink任务，避免阻塞Spring Boot启动
        new Thread(this::runFlinkCDC, "flink-cdc-monitor-thread").start();
    }

    /**
     * 运行Flink CDC监控任务
     */
    private void runFlinkCDC() {
        try {
            // 解析JDBC URL获取主机和端口
            String host = parseHostFromJdbcUrl(jdbcUrl);
            int port = parsePortFromJdbcUrl(jdbcUrl);
            // 解析数据库名
            String database = parseDatabaseFromJdbcUrl(jdbcUrl);

            // 1. 创建Flink执行环境
            final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
            // 单并行度保证顺序
            env.setParallelism(1);
            // 核心检查点配置
            env.enableCheckpointing(10_000); // 10秒间隔
            CheckpointConfig checkpointConfig = env.getCheckpointConfig();
            // EXACTLY_ONCE：确保每条 CDC 变更事件精确处理一次，不会丢失变更，也不会重复处理，例如：账户余额变更、库存扣减
            // AT_LEAST_ONCE：变更事件可能被重复处理，需要下游系统做幂等处理：例如：点击流计数（可接受小误差）
            checkpointConfig.setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
            checkpointConfig.setCheckpointTimeout(120_000); // 2分钟超时
            checkpointConfig.setMinPauseBetweenCheckpoints(5_000); // 5秒最小间隔
            checkpointConfig.setTolerableCheckpointFailureNumber(2); // 容忍2次失败

            // 2. 配置MySQL CDC源
            MySqlSource<String> mySqlSource = MySqlSource.<String>builder()
                    // 数据库连接信息
                    .hostname(host)
                    .port(port)
                    .databaseList(database)  // 从URL解析的数据库名
                    .tableList(table)        // 监控的表名
                    .username(username)
                    .password(password)
                    // 启动选项：从最新位置开始读取
                    .startupOptions(StartupOptions.latest())
                    // 反序列化器：将CDC事件转换为JSON字符串
                    .deserializer(new JsonDebeziumDeserializationSchema())
                    .serverId(serverId)
                    .serverTimeZone("GMT+8")  // 从URL解析的时区
                    .build();

            // 3. 创建数据源
            DataStream<String> stream = env.fromSource(
                    mySqlSource,
                    WatermarkStrategy.noWatermarks(),
                    "MySQL CDC Source"
            );

            // 4. 处理捕获到的变更数据
            stream.process(new QuestionBankProcessFunction());

            // 5. 打印变更数据到控制台
            stream.print().setParallelism(1);

            // 6. 执行Flink作业
            env.execute("Question Bank CDC Monitor");

        } catch (Exception e) {
            // 记录错误日志并处理异常
            System.err.println("Flink CDC监控任务执行失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 从JDBC URL解析主机名
     * 支持格式: ******************************************
     */
    private String parseHostFromJdbcUrl(String jdbcUrl) {
        Pattern pattern = Pattern.compile("jdbc:mysql://([^:]+):?.*");
        Matcher matcher = pattern.matcher(jdbcUrl);
        if (matcher.find()) {
            return matcher.group(1);
        }
        // 默认值
        return "domino-admin.site";
    }

    /**
     * 从JDBC URL解析端口号
     * 支持格式: ******************************************
     */
    private int parsePortFromJdbcUrl(String jdbcUrl) {
        Pattern pattern = Pattern.compile("jdbc:mysql://[^:]+:(\\d+)/?.*");
        Matcher matcher = pattern.matcher(jdbcUrl);
        if (matcher.find()) {
            return Integer.parseInt(matcher.group(1));
        }
        // 默认MySQL端口
        return 3306;
    }

    /**
     * 从JDBC URL解析数据库名
     * 支持格式: ******************************************
     */
    private String parseDatabaseFromJdbcUrl(String jdbcUrl) {
        Pattern pattern = Pattern.compile("jdbc:mysql://[^/]+/([^?]+)\\??.*");
        Matcher matcher = pattern.matcher(jdbcUrl);
        if (matcher.find()) {
            return matcher.group(1);
        }
        // 默认数据库名
        return "qh";
    }
}
