# 试卷导出功能分析和优化建议

## 当前实现状态

### ✅ 已实现的功能

1. **题目序号显示控制** (`showQuestionNumbers`)
   - 位置：`QhPaperExportServiceImpl.java` 第229-233行
   - 功能：根据参数控制是否显示题目序号（1. 2. 3. ...）

2. **分数显示控制** (`showScores`)
   - 位置：`QhPaperExportServiceImpl.java` 第239-241行
   - 功能：根据参数控制是否显示题目分数（如：（5分））

3. **分割线功能**
   - 位置：`QhPaperExportServiceImpl.java` 第212-214行
   - 作用：在试卷信息和题目内容之间添加水平分割线，提升视觉效果

### 🔧 新增优化功能

4. **试卷信息显示控制** (`showPaperInfo`)
   - 新增参数：控制是否显示试卷信息部分
   - 包含：科目、年级、地区、年份、题目数量、总分等信息

## 参数说明

### 前端传入的参数示例
```json
{
  "paperId": "f82d5a8ffb604d71bcbd3ab53d8b28ba",
  "exportType": "PDF",
  "contentOptions": {
    "includeQuestions": true,
    "includeAnswers": true,
    "includeAnalysis": false
  },
  "formatOptions": {
    "pageSize": "A3",
    "fontSize": 10,
    "showQuestionNumbers": false,  // 控制题目序号显示
    "showScores": false,          // 控制分数显示
    "showPaperInfo": true,        // 新增：控制试卷信息显示
    "margin": 20,
    "lineSpacing": 1.5
  },
  "flag": "ZJ"
}
```

### 各参数的作用

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `showQuestionNumbers` | Boolean | true | 是否显示题目序号（1. 2. 3. ...） |
| `showScores` | Boolean | true | 是否显示题目分数（如：（5分）） |
| `showPaperInfo` | Boolean | true | 是否显示试卷信息（科目、年级等） |

## 分割线的作用

分割线（`builder.insertHorizontalRule()`）的作用：
- **视觉分隔**：在试卷信息和题目内容之间添加一条水平线
- **格式美化**：让试卷结构更清晰，便于阅读
- **专业外观**：符合正式试卷的排版规范

如果不需要分割线，可以在 `addPaperInfo` 方法中注释掉相关代码。

## 建议

1. **保留分割线**：建议保留分割线功能，它能显著提升试卷的专业外观
2. **参数验证**：前端应确保传入正确的布尔值
3. **默认值设置**：后端已设置合理的默认值，确保功能稳定性

## 测试建议

建议测试以下场景：
1. 所有显示选项都为 true
2. 所有显示选项都为 false  
3. 混合显示选项
4. 空值或缺失参数的处理
