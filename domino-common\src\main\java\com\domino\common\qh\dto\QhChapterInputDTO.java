package com.domino.common.qh.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * 章节信息
 *
 */

@Data
public class QhChapterInputDTO implements Serializable {
    private static final long serialVersionUID = 357057176070479729L;

    private String chapterId;

    private String gradeId;

    @NotBlank(message = "章节名称不能为空")
    private String chapterName;

    private String parentChapterId;

    private List<QhChapterInputDTO> childrenList;
}
