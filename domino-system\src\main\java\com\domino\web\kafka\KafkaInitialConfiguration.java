// package com.domino.web.kafka;
//
// import org.apache.kafka.clients.admin.NewTopic;
// import org.springframework.context.annotation.Bean;
// import org.springframework.context.annotation.Configuration;
//
// @Configuration
// public class KafkaInitialConfiguration {
//
//     public static final String test = "test-topic";
//
//     // 创建一个名为 test 的Topic并设置分区数为3，分区副本数为2
//     @Bean
//     public NewTopic initialDeleteTopic() {
//         // return new NewTopic(TOPIC_Task,3, (short) 2 );
//         return new NewTopic(test, 3, (short) 1);
//     }
// }
