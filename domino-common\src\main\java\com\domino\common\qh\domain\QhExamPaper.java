package com.domino.common.qh.domain;

import com.domino.common.annotation.Excel;
import com.domino.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

public class QhExamPaper extends BaseEntity {
    private static final long serialVersionUID = 7032848879787378875L;

    /**
     * 主键
     */
    private String id;

    //private String  题库ID

    private String paperName;

    /**
     * 试卷类型
     */
    @Excel(name = "试卷类型")
    private String paperType;

    /**
     * 试卷状态
     */
    @Excel(name = "试卷状态")
    private String paperStyle;

    /**
     * 试卷来源
     */
    @Excel(name = "试卷来源")
    private String sourcePaper;

    /**
     * 试卷所属地区
     */
    @Excel(name = "试卷所属地区")
    private String region;

    /**
     * 试卷所属地区
     */
    @Excel(name = "试卷年份")
    private String pyear;

    /**
     * 试卷题目数量
     */
    @Excel(name = "题目数量")
    private String num;

    /**
     * 试卷总分
     */
    @Excel(name = "总分")
    private String score;

    /**
     * 科目
     */
    @Excel(name = "科目")
    private String subject;

    /**
     * 年级
     */
    @Excel(name = "年级")
    private String grade;

    /**
     * 创建人
     */
    @Excel(name = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    /**
     * 更新人
     */
    @Excel(name = "更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateTime;

    /**
     * 试卷删除标志（0代表存在 1代表删除）
     */
    private String delFlag;

    /**
     * 组卷删除标志（0代表存在 1代表删除）
     */
    private String gdelFlag;

    private String folder;

    private String flag;

    public String getPaperStyle() {
        return paperStyle;
    }

    public void setPaperStyle(String paperStyle) {
        this.paperStyle = paperStyle;
    }

    public String getNum() {
        return num;
    }

    public void setNum(String num) {
        this.num = num;
    }

    public String getScore() {
        return score;
    }

    public void setScore(String score) {
        this.score = score;
    }

    public String getPyear() {
        return pyear;
    }

    public void setPyear(String pyear) {
        this.pyear = pyear;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPaperName() {
        return paperName;
    }

    public void setPaperName(String paperName) {
        this.paperName = paperName;
    }

    public String getPaperType() {
        return paperType;
    }

    public void setPaperType(String paperType) {
        this.paperType = paperType;
    }

    public String getSourcePaper() {
        return sourcePaper;
    }

    public void setSourcePaper(String sourcePaper) {
        this.sourcePaper = sourcePaper;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    @Override
    public String getCreateBy() {
        return createBy;
    }

    @Override
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public String getUpdateBy() {
        return updateBy;
    }

    @Override
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    @Override
    public Date getUpdateTime() {
        return updateTime;
    }

    @Override
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getGrade() {
        return grade;
    }

    public void setGrade(String grade) {
        this.grade = grade;
    }

    public String getGdelFlag() {
        return gdelFlag;
    }

    public void setGdelFlag(String gdelFlag) {
        this.gdelFlag = gdelFlag;
    }

    public String getFolder() {
        return folder;
    }

    public void setFolder(String folder) {
        this.folder = folder;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }

    @Override
    public String toString() {
        return "QhExamPaper{" +
                "id='" + id + '\'' +
                ", paperName='" + paperName + '\'' +
                ", paperType='" + paperType + '\'' +
                ", paperStyle='" + paperStyle + '\'' +
                ", sourcePaper='" + sourcePaper + '\'' +
                ", region='" + region + '\'' +
                ", pyear='" + pyear + '\'' +
                ", num='" + num + '\'' +
                ", score='" + score + '\'' +
                ", createBy='" + createBy + '\'' +
                ", createTime=" + createTime +
                ", updateBy='" + updateBy + '\'' +
                ", updateTime=" + updateTime +
                ", delFlag='" + delFlag + '\'' +
                '}';
    }
}
