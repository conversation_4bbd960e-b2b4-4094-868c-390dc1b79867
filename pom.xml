<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>domino</groupId>
    <artifactId>domino-project</artifactId>
    <version>3.8.7</version>
    <packaging>pom</packaging>

    <name>domino-project</name>
    <url>未定</url>
    <description>多米诺任务管理</description>

    <modules>
        <module>domino-admin</module>
        <module>domino-common</module>
        <module>domino-framework</module>
        <module>domino-generator</module>
        <module>domino-quartz</module>
        <module>domino-system</module>
    </modules>

    <properties>
        <domino.version>3.8.7</domino.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <maven-jar-plugin.version>3.1.1</maven-jar-plugin.version>
        <mybatis-plus.version>3.5.3</mybatis-plus.version>
        <idworker.version>1.5.0</idworker.version>
        <p6spy.version>3.9.1</p6spy.version>
        <!-- 8.5.10版本的minio有依赖冲突 -->
        <minio.version>8.2.1</minio.version>
        <!--        <zookeeper.version>3.6.3</zookeeper.version>-->
        <elasticsearch-client>7.15.2</elasticsearch-client>
        <druid.version>1.2.20</druid.version>
        <bitwalker.version>1.21</bitwalker.version>
        <swagger.version>3.0.0</swagger.version>
        <kaptcha.version>2.3.3</kaptcha.version>
        <pagehelper.boot.version>1.4.7</pagehelper.boot.version>
        <fastjson.version>2.0.43</fastjson.version>
        <oshi.version>6.4.11</oshi.version>
        <commons.io.version>2.13.0</commons.io.version>
        <commons.collections.version>3.2.2</commons.collections.version>
        <poi.version>4.1.2</poi.version>
        <velocity.version>2.3</velocity.version>
        <jwt.version>0.9.1</jwt.version>
        <redisson.version>3.16.2</redisson.version>
        <hutool.version>5.8.25</hutool.version>
        <tess4j.version>5.8.0</tess4j.version>
        <jai-imageio-core.version>1.4.0</jai-imageio-core.version>
        <aliyun_ocr_api.version>3.1.2</aliyun_ocr_api.version>
    </properties>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>
            <!-- SpringBoot的依赖配置-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>2.5.15</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- 阿里数据库连接池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <!-- 解析客户端操作系统、浏览器等 -->
            <dependency>
                <groupId>eu.bitwalker</groupId>
                <artifactId>UserAgentUtils</artifactId>
                <version>${bitwalker.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.imadcn.framework</groupId>
                <artifactId>idworker</artifactId>
                <version>${idworker.version}</version>
            </dependency>

            <dependency>
                <groupId>p6spy</groupId>
                <artifactId>p6spy</artifactId>
                <version>${p6spy.version}</version>
            </dependency>

            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
            </dependency>

            <!--            <dependency>-->
            <!--                <groupId>org.apache.zookeeper</groupId>-->
            <!--                <artifactId>zookeeper</artifactId>-->
            <!--                <version>${zookeeper.version}</version>-->
            <!--            </dependency>-->

            <!-- pagehelper 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.boot.version}</version>
            </dependency>

            <!--Elasticsearch的核心库-->
            <!--Elasticsearch的核心库，并不是必须引入，如果缺少依赖或有运行时异常时引入-->
            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>elasticsearch</artifactId>
                <version>${elasticsearch-client}</version>
            </dependency>

            <!--Elasticsearch 低级别REST客户端-->
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-client</artifactId>
                <version>${elasticsearch-client}</version>
            </dependency>

            <!--Elasticsearch 高级别REST客户端-->
            <!--默认的es中用的是低级别客户端，此处引入高级别客户端，并单独指定版本使用-->
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-high-level-client</artifactId>
                <version>${elasticsearch-client}</version>
            </dependency>

            <!-- 获取系统信息 -->
            <dependency>
                <groupId>com.github.oshi</groupId>
                <artifactId>oshi-core</artifactId>
                <version>${oshi.version}</version>
            </dependency>

            <!-- Swagger3依赖 -->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-boot-starter</artifactId>
                <version>${swagger.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.swagger</groupId>
                        <artifactId>swagger-models</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- io常用工具类 -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>

            <!-- excel工具 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <!-- velocity代码生成使用模板 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <!-- collections工具类 -->
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>${commons.collections.version}</version>
            </dependency>

            <!-- 阿里JSON解析器 -->
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!-- Token生成与解析-->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jwt.version}</version>
            </dependency>

            <!-- 验证码 -->
            <dependency>
                <groupId>pro.fessional</groupId>
                <artifactId>kaptcha</artifactId>
                <version>${kaptcha.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <!-- Redisson 锁功能 -->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <!-- Tess4J -->
            <dependency>
                <groupId>net.sourceforge.tess4j</groupId>
                <artifactId>tess4j</artifactId>
                <version>${tess4j.version}</version>
            </dependency>

            <!-- 图片处理 -->
            <dependency>
                <groupId>com.github.jai-imageio</groupId>
                <artifactId>jai-imageio-core</artifactId>
                <version>${jai-imageio-core.version}</version>
            </dependency>

            <!-- 阿里云OCR图片处理 -->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>ocr_api20210707</artifactId>
                <version>${aliyun_ocr_api.version}</version>
            </dependency>

            <!-- 定时任务-->
            <dependency>
                <groupId>domino</groupId>
                <artifactId>domino-admin</artifactId>
                <version>${domino.version}</version>
            </dependency>

            <!-- 通用工具-->
            <dependency>
                <groupId>domino</groupId>
                <artifactId>domino-common</artifactId>
                <version>${domino.version}</version>
            </dependency>

            <!-- 核心模块-->
            <dependency>
                <groupId>domino</groupId>
                <artifactId>domino-framework</artifactId>
                <version>${domino.version}</version>
            </dependency>

            <!-- 代码生成模块-->
            <dependency>
                <groupId>domino</groupId>
                <artifactId>domino-generator</artifactId>
                <version>${domino.version}</version>
            </dependency>

            <!-- 定时任务-->
            <dependency>
                <groupId>domino</groupId>
                <artifactId>domino-quartz</artifactId>
                <version>${domino.version}</version>
            </dependency>

            <!-- 系统模块-->
            <dependency>
                <groupId>domino</groupId>
                <artifactId>domino-system</artifactId>
                <version>${domino.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
