package com.domino.common.qh.dto;

import lombok.Data;

import java.util.List;

@Data
public class QhQuestionDTO {

    private String id;

    private String gradeId;      // 年级

    private String paperType;  //试卷类型

    private String questionType; // 题型

    private Integer requiredNumber; // 题目数量

    private String difficulty;   // 难度

    private String score; // 分值

    private String year; //年份

    private String region; //地区

    private List<String> chapters;    //章节ID列表

    private List<String> knowledgePoints; // 知识点ID列表

    private List<String> tags; // 标签

}
