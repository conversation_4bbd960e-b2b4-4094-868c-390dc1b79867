<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.domino.qh.mapper.QhKnowledgeTreeMapper">

    <resultMap type="QhKnowledgeTree" id="QhKnowledgeTreeResult">
        <id property="id" column="id"/>
        <result property="parentId" column="parent_id"/>
        <result property="ancestors" column="ancestors"/>
        <result property="name" column="name"/>
        <result property="orderNum" column="order_num"/>
        <result property="nodeType" column="node_type"/>
        <result property="shareType" column="share_type"/>
        <result property="status" column="status"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectKnowledgeVo">
        select d.id,
               d.parent_id,
               d.ancestors,
               d.name,
               d.order_num,
               d.node_type,
               d.share_type,
               d.status,
               d.remark,
               d.del_flag,
               d.create_by,
               d.create_time,
               d.update_time,
               d.update_by
        from qh_knowledge_tree d
    </sql>

    <select id="selectKnowledgeList" parameterType="QhKnowledgeTree" resultMap="QhKnowledgeTreeResult">
        <include refid="selectKnowledgeVo"/>
        where d.del_flag = '0'
        <if test="knowledge.id != null and knowledge.id != ''">
            AND id = #{knowledge.id}
        </if>
        <if test="knowledge.parentId != null and knowledge.parentId != ''">
            AND parent_id = #{knowledge.parentId}
        </if>
        <if test="knowledge.name != null and knowledge.name != ''">
            AND name like concat('%', #{knowledge.name}, '%')
        </if>
        <if test="knowledge.nodeType != null and knowledge.nodeType != ''">
            AND node_type = #{knowledge.nodeType}
        </if>
        <!-- 新增动态条件处理 -->
        <choose>
            <when test="createBy != null and createBy != ''">
                <choose>
                    <when test="createBy.equals(currentUser)">
                        AND create_by = #{createBy}
                    </when>
                    <otherwise>
                        AND share_type = '1' AND create_by = #{createBy}
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                AND (share_type = '1' OR create_by = #{currentUser})
            </otherwise>
        </choose>
        <if test="knowledge.status != null and knowledge.status != ''">
            AND status = #{knowledge.status}
        </if>
        <if test="knowledge.ancestorIdList != null and knowledge.ancestorIdList.size() > 0 ">
            <foreach collection="knowledge.ancestorIdList " item="item" index="index" open=" AND (" close=")" separator=" OR ">
                (find_in_set(#{item}, ancestors) OR #{item} = d.id)
            </foreach>
        </if>
        <!-- 优先展示共享题库，再展示个人题库 -->
        order by FIELD(share_type, '1', '2'), d.parent_id, d.order_num, d.create_time
    </select>

    <select id="selectCount" parameterType="Map" resultType="Integer">
        SELECT COUNT(*)
        FROM qh_knowledge_tree
        WHERE del_flag = '0'
        <if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
            AND date_format(create_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
        </if>
        <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
            AND date_format(create_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
        </if>
    </select>

    <select id="selectKnowledgeById" parameterType="String" resultMap="QhKnowledgeTreeResult">
        select d.id,
               d.parent_id,
               d.ancestors,
               d.name,
               d.order_num,
               d.node_type,
               d.share_type,
               d.status,
               d.remark,
               (select name from qh_knowledge_tree where id = d.parent_id) parent_name
        from qh_knowledge_tree d
        where d.del_flag = '0'
          AND d.id = #{id}
    </select>

    <!-- 批量查询知识树节点 -->
    <select id="selectKnowledgeByIds" resultMap="QhKnowledgeTreeResult">
        SELECT *
        FROM qh_knowledge_tree
        WHERE del_flag = '0' AND id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="hasChildById" parameterType="String" resultType="int">
        select count(1)
        from qh_knowledge_tree
        where del_flag = '0'
          and parent_id = #{id}
        limit 1
    </select>

    <select id="selectChildrenKnowledgeById" parameterType="String" resultMap="QhKnowledgeTreeResult">
        select *
        from qh_knowledge_tree
        where find_in_set(#{id}, ancestors)
    </select>

    <select id="selectNormalChildrenKnowledgeById" parameterType="String" resultType="int">
        select count(*)
        from qh_knowledge_tree
        where status = 0
          and del_flag = '0'
          and find_in_set(#{id}, ancestors)
    </select>

    <select id="checkKnowledgeNameUnique" resultMap="QhKnowledgeTreeResult">
        <include refid="selectKnowledgeVo"/>
        where name = #{name} and parent_id = #{parentId} and del_flag = '0' limit 1
    </select>

    <insert id="insertKnowledge" parameterType="QhKnowledgeTree">
        insert into qh_knowledge_tree(
        <if test="id != null and id != ''">id,</if>
        <if test="parentId != null and parentId != ''">parent_id,</if>
        <if test="name != null and name != ''">name,</if>
        <if test="ancestors != null and ancestors != ''">ancestors,</if>
        <if test="orderNum != null">order_num,</if>
        <if test="nodeType != null">node_type,</if>
        <if test="shareType != null">share_type,</if>
        <if test="status != null">status,</if>
        <if test="remark != null">remark,</if>
        <if test="delFlag != null">del_flag,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        create_time
        )values(
        <if test="id != null and id != ''">#{id},</if>
        <if test="parentId != null and parentId != ''">#{parentId},</if>
        <if test="name != null and name != ''">#{name},</if>
        <if test="ancestors != null and ancestors != ''">#{ancestors},</if>
        <if test="orderNum != null">#{orderNum},</if>
        <if test="nodeType != null">#{nodeType},</if>
        <if test="shareType != null">#{shareType},</if>
        <if test="status != null">#{status},</if>
        <if test="remark != null">#{remark},</if>
        <if test="delFlag != null">#{delFlag},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        sysdate()
        )
    </insert>

    <update id="updateKnowledge" parameterType="QhKnowledgeTree">
        update qh_knowledge_tree
        <set>
            <if test="parentId != null and parentId != ''">parent_id = #{parentId},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="ancestors != null and ancestors != ''">ancestors = #{ancestors},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="nodeType != null and nodeType != ''">node_type = #{nodeType},</if>
            <if test="shareType != null and shareType != ''">share_type = #{shareType},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = sysdate()
        </set>
        where id = #{id}
    </update>

    <update id="updateKnowledgeChildren" parameterType="java.util.List">
        update qh_knowledge_tree set ancestors =
        <foreach collection="knowledgeTree" item="item" index="index"
                 separator=" " open="case id" close="end">
            when #{item.id} then #{item.ancestors}
        </foreach>
        where id in
        <foreach collection="knowledgeTree" item="item" index="index"
                 separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>

    <update id="updateKnowledgeStatusNormal" parameterType="String">
        update qh_knowledge_tree set status = '0' where id in
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <delete id="deleteKnowledgeById" parameterType="String">
        update qh_knowledge_tree
        set del_flag = '2'
        where id = #{id}
    </delete>

    <select id="selectKnowledgeIdsByChapterIds" resultType="java.lang.String">
        SELECT id FROM qh_knowledge_tree
        WHERE node_type = '5'
        AND (
        <foreach collection="chapterIds" item="chapterId" separator=" OR ">
            FIND_IN_SET(#{chapterId}, ancestors) > 0
        </foreach>
        )
    </select>

</mapper>
