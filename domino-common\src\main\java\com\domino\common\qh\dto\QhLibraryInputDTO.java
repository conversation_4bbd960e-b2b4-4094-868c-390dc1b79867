package com.domino.common.qh.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * 题库信息
 *
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QhLibraryInputDTO implements Serializable {
    private static final long serialVersionUID = -8231724586061700933L;

    private String libraryId;

    private String userId;  //存疑

    private String libraryName; //初中数学题库（2023版）

    private String description;  //覆盖全国初中数学真题及模拟题

    private Boolean isLocal;  //true

    private Boolean isCloud;  //true

    private Boolean isPrivate; //false

    private Boolean isPublic; //true

    @NotBlank(message = "创建时间不能为空")
    private String createTime; //2023-10-01 08:00:00

    private Boolean isHardcode;  //是否传输主键

    //年级与章节信息
    private QhGradeInputDTO gradeInputDTO;  //目前先按一个题库下只有一个年级来写

    //知识点信息
    private List<QhKnowledgeInputDTO> knowledgeInputDTOList;

    //知识点和题目关联信息
    private List<QhKnowledgeQuestionDTO> knowledgeQuestionDTOList;

    //题目信息
    private List<QhQuestionInputDTO> questionInputDTOList;

    //试卷信息
    private List<QhPaperInputDTO> paperInputDTOList;

}
