<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.domino.qh.mapper.QhQuestionBankMapper">

    <resultMap type="QhQuestionBank" id="QuestionBankResult">
        <id property="id" column="id"/>
        <result property="gradeId" column="grade_id"/>
        <result property="questionType" column="question_type"/>
        <result property="context" column="context"/>
        <result property="ocrText" column="ocr_text"/>
        <result property="questionAnalyze" column="question_analyze"/>
        <result property="questionAnswer" column="question_answer"/>
        <result property="score" column="score"/>
        <result property="difficulty" column="difficulty"/>
        <result property="paperType" column="paper_type"/>
        <result property="sourcePaper" column="source_paper"/>
        <result property="year" column="year"/>
        <result property="region" column="region"/>
        <result property="tag" column="tag"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="update_by"/>
        <collection property="knowledgeTreeList" javaType="List" resultMap="QhKnowledgeResult"/>
    </resultMap>

    <resultMap type="QhKnowledgeTree" id="QhKnowledgeResult">
        <id property="id" column="id"/>
        <result property="parentId" column="parent_id"/>
        <result property="ancestors" column="ancestors"/>
        <result property="name" column="name"/>
        <result property="orderNum" column="order_num"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectQuestionBankVo">
        select u.id,
               u.grade_id,
               u.question_type,
               u.context,
               u.question_analyze,
               u.question_answer,
               u.score,
               u.difficulty,
               u.paper_type,
               u.source_paper,
               u.year,
               u.region,
               u.tag,
               u.del_flag,
               u.create_by,
               u.create_time,
               d.id,
               d.parent_id,
               d.ancestors,
               d.name,
               d.order_num,
               d.status as knowledge_status
        from qh_question_bank u
                 left join qh_knowledge_tree d on u.grade_id = d.id

    </sql>

    <select id="selectQuestionBankList" parameterType="QhQuestionBank" resultMap="QuestionBankResult">
        SELECT
        qb.id,
        qb.grade_id,
        qb.question_type,
        qb.context,
        qb.question_analyze,
        qb.question_answer,
        qb.score,
        qb.difficulty,
        qb.paper_type,
        qb.source_paper,
        qb.year,
        qb.region,
        qb.tag,
        qb.del_flag,
        qb.create_by,
        qb.create_time
        FROM qh_question_bank qb
        WHERE qb.del_flag = '0'
        <if test="id != null and id != ''">
            AND qb.id = #{id}
        </if>
        <if test="difficulty != null and difficulty != ''">
            AND qb.difficulty = #{difficulty}
        </if>
        <if test="questionType != null and questionType != ''">
            AND qb.question_type = #{questionType}
        </if>
        <if test="sourcePaper != null and sourcePaper != ''">
            AND qb.source_paper like lower(concat('%', #{sourcePaper}, '%'))
        </if>
<!--        <if test="region != null and region != ''">-->
<!--            AND qb.region like lower(concat('%', #{region}, '%'))-->
<!--        </if>-->
        <if test="tag != null and tag != ''">
            AND qb.tag like lower(concat('%', #{tag}, '%'))
        </if>
        <if test="difficultyList != null and difficultyList.size() > 0 ">
            AND qb.difficulty IN
            <foreach collection="difficultyList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="questionTypeList != null and questionTypeList.size() > 0 ">
            AND qb.question_type IN
            <foreach collection="questionTypeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="paperTypeList != null and paperTypeList.size() > 0 ">
            AND qb.paper_type IN
            <foreach collection="paperTypeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="yearList != null and yearList.size() > 0 ">
            AND qb.year IN
            <foreach collection="yearList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        AND qb.id IN
        (
            SELECT kq.question_bank_id
            FROM qh_knowledge_question kq
            WHERE kq.knowledge_tree_id IN
            (
                SELECT kt.id
                FROM qh_knowledge_tree kt
                WHERE
                <foreach collection="knowledgeTreeIds" item="item" separator=" OR ">
                    FIND_IN_SET(#{item}, kt.ancestors) > 0
                    OR kt.id = #{item}
                </foreach>
            )
        )
    </select>

    <select id="selectCount" parameterType="Map" resultType="Integer">
        SELECT COUNT(*)
        FROM qh_question_bank
        WHERE del_flag = '0'
        <if test="difficulty != null and difficulty != ''">
            AND difficulty = #{difficulty}
        </if>
        <if test="questionType != null and questionType != ''">
            AND question_type = #{questionType}
        </if>
        <if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
            AND date_format(create_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
        </if>
        <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
            AND date_format(create_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
        </if>
    </select>

    <select id="selectQuestionBankBatchByIds" resultMap="QuestionBankResult">
        SELECT
        qb.id,
        qb.grade_id,
        qb.question_type,
        qb.context,
        qb.question_analyze,
        qb.question_answer,
        qb.score,
        qb.difficulty,
        qb.paper_type,
        qb.source_paper,
        qb.year,
        qb.region,
        qb.tag,
        qb.del_flag,
        qb.create_by,
        qb.create_time
        FROM qh_question_bank qb
        WHERE qb.del_flag = '0' AND qb.id IN
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        ORDER BY FIELD(qb.question_type, '1', '2', '5', '4') ASC
    </select>

    <select id="selectQuestionBankBatchByIdsOrderByIds" resultMap="QuestionBankResult">
        SELECT
        qb.id,
        qb.grade_id,
        qb.question_type,
        qb.context,
        qb.question_analyze,
        qb.question_answer,
        qb.score,
        qb.difficulty,
        qb.paper_type,
        qb.source_paper,
        qb.year,
        qb.region,
        qb.tag,
        qb.del_flag,
        qb.create_by,
        qb.create_time
        FROM qh_question_bank qb
        WHERE qb.del_flag = '0' AND qb.id IN
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        ORDER BY FIELD(qb.id,
        <foreach collection="ids" item="item" separator=",">
            #{item}
        </foreach>)
    </select>


    <select id="selectQuestionBankById" parameterType="String" resultMap="QuestionBankResult">
        <include refid="selectQuestionBankVo"/>
        where u.id = #{id}
    </select>

    <insert id="insertQuestionBank" parameterType="QhQuestionBank" useGeneratedKeys="true" keyProperty="id">
        insert into qh_question_bank(
        <if test="id != null and id != ''">id,</if>
        <if test="gradeId != null and gradeId != ''">grade_id,</if>
        <if test="questionType != null and questionType != ''">question_type,</if>
        <if test="context != null and context != ''">context,</if>
        <if test="ocrText != null and ocrText != ''">ocr_text,</if>
        <if test="questionAnalyze != null and questionAnalyze != ''">question_analyze,</if>
        <if test="questionAnswer != null and questionAnswer != ''">question_answer,</if>
        <if test="score != null and score != ''">score,</if>
        <if test="difficulty != null and difficulty != ''">difficulty,</if>
        <if test="paperType != null and paperType != ''">paper_type,</if>
        <if test="sourcePaper != null and sourcePaper != ''">source_paper,</if>
        <if test="year != null and year != ''">year,</if>
        <if test="region != null and region != ''">region,</if>
        <if test="tag != null and tag != ''">tag,</if>
        <if test="delFlag != null and delFlag != ''">del_flag,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        create_time
        )values(
        <if test="id != null and id != ''">#{id},</if>
        <if test="gradeId != null and gradeId != ''">#{gradeId},</if>
        <if test="questionType != null and questionType != ''">#{questionType},</if>
        <if test="context != null and context != ''">#{context},</if>
        <if test="ocrText != null and ocrText != ''">#{ocrText},</if>
        <if test="questionAnalyze != null and questionAnalyze != ''">#{questionAnalyze},</if>
        <if test="questionAnswer != null and questionAnswer != ''">#{questionAnswer},</if>
        <if test="score != null and score != ''">#{score},</if>
        <if test="difficulty != null and difficulty != ''">#{difficulty},</if>
        <if test="paperType != null and paperType != ''">#{paperType},</if>
        <if test="sourcePaper != null and sourcePaper != ''">#{sourcePaper},</if>
        <if test="year != null and year != ''">#{year},</if>
        <if test="region != null and region != ''">#{region},</if>
        <if test="tag != null and tag != ''">#{tag},</if>
        <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        sysdate()
        )
    </insert>

    <update id="updateQuestionBank" parameterType="QhQuestionBank">
        update qh_question_bank
        <set>
            <if test="gradeId != null and gradeId != 0">grade_id = #{gradeId},</if>
            <if test="questionType != null and questionType != ''">question_type = #{questionType},</if>
            <if test="context != null and context != ''">context = #{context},</if>
            <if test="ocrText != null and ocrText != ''">ocr_text = #{ocrText},</if>
            <if test="questionAnalyze != null and questionAnalyze != ''">`question_analyze` = #{questionAnalyze},</if>
            <if test="questionAnswer != null and questionAnswer != ''">`question_answer` = #{questionAnswer},</if>
            <if test="score != null and score != ''">score = #{score},</if>
            <if test="difficulty != null and difficulty != ''">difficulty = #{difficulty},</if>
            <if test="paperType != null and paperType != ''">paper_type = #{paperType},</if>
            <if test="sourcePaper != null and sourcePaper != ''">source_paper = #{sourcePaper},</if>
            <if test="year != null and year != ''">year = #{year},</if>
            <if test="region != null and region != ''">region = #{region},</if>
            <if test="tag != null and tag != ''">tag = #{tag},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = sysdate()
        </set>
        where id = #{id}
    </update>

    <delete id="deleteQuestionBankById" parameterType="QhQuestionBank">
        update qh_question_bank
        set del_flag = '2'
        where id = #{id} AND create_by = #{createBy}
    </delete>

    <delete id="deleteQuestionBankByIds" parameterType="Long">
        update qh_question_bank
        set del_flag = '2'
        where create_by = #{createBy} AND id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteQuestionBankByPaperId" parameterType="QhQuestionBank">
        update qh_question_bank
        set del_flag = '2'
        where source_paper = #{paperId} AND create_by = #{createBy}
    </delete>

    <select id="selectBankList" resultType="com.domino.common.qh.domain.QhQuestionBank">
        select b.id,
        b.grade_id,
        b.question_type,
        b.context,
        b.question_analyze,
        b.question_answer,
        b.score,
        b.difficulty,
        b.paper_type,
        b.source_paper,
        b.year,
        b.region,
        b.tag,
        b.del_flag,
        b.create_by,
        b.create_time
        from qh_question_bank b
        WHERE b.del_flag = '0' AND 1=1
        <if test="bank.gradeId != null and bank.gradeId != ''">
            AND b.grade_id = #{bank.gradeId}
        </if>
        <if test="bank.questionType != null and bank.questionType != ''">
            AND b.question_type = #{bank.questionType}
        </if>
        <if test="bank.difficulty != null">
            AND b.difficulty = #{bank.difficulty}
        </if>
        <if test="bank.score != null and bank.score != ''">
            AND b.score = #{bank.score}
        </if>
        <if test="bank.year != null and bank.year != ''">
            AND b.year = #{bank.year}
        </if>
        <if test="bank.region != null and bank.region != ''">
            AND b.region = #{bank.region}
        </if>
        <if test="bank.sourcePaper != null and bank.sourcePaper != ''">
            AND b.source_paper = #{bank.sourcePaper}
        </if>
        <if test="bank.tag != null and bank.tag != ''">
            AND b.tag = like lower(concat('%', #{tag}, '%'))
        </if>
        <if test="bank.knowledgeTreeIds != null and bank.knowledgeTreeIds.size() > 0">
            AND EXISTS (
            SELECT 1 FROM qh_knowledge_question kq
            WHERE kq.question_bank_id = b.id
            AND kq.knowledge_tree_id IN
            <foreach item="item" collection="bank.knowledgeTreeIds" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        ORDER BY FIELD(b.question_type, '1', '2', '5', '4') ASC
    </select>

    <select id="selectAll" resultType="com.domino.common.qh.domain.QhQuestionBank">
        select *
        from qh_question_bank b
        WHERE 1=1
    </select>

    <select id="selectByCondition" resultType="QhQuestionBank">
        SELECT b.*
        FROM qh_question_bank b
        INNER JOIN qh_exam_paper e ON b.source_paper = e.id
        <where>
            e.paper_style = '4'
            <if test="condition.questionType != null and condition.questionType != ''">
                AND b.question_type = #{condition.questionType}
            </if>
            <if test="condition.minDifficulty != null">
                AND CAST(b.difficulty AS DECIMAL(5,2)) <![CDATA[ >=  ]]> #{condition.minDifficulty}
            </if>
            <if test="condition.maxDifficulty != null">
                AND CAST(b.difficulty AS DECIMAL(5,2)) <![CDATA[ <=  ]]> #{condition.maxDifficulty}
            </if>
            <if test="condition.years != null and !condition.years.isEmpty()">
                AND e.pyear IN
                <foreach item="year" collection="condition.years" open="(" separator="," close=")">
                    #{year}
                </foreach>
            </if>
            <if test="condition.grades != null and !condition.grades.isEmpty()">
                AND e.grade IN
                <foreach item="grade" collection="condition.grades" open="(" separator="," close=")">
                    #{grade}
                </foreach>
            </if>
            <if test="condition.regions != null and !condition.regions.isEmpty()">
                AND e.region IN
                <foreach item="region" collection="condition.regions" open="(" separator="," close=")">
                    #{region}
                </foreach>
            </if>
            <if test="condition.knowledgeTreeIds != null and condition.knowledgeTreeIds.size() > 0">
                AND EXISTS (
                SELECT 1
                FROM qh_knowledge_question kq
                WHERE kq.question_bank_id = b.id
                AND kq.knowledge_tree_id IN
                <foreach item="item" collection="condition.knowledgeTreeIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
            AND CAST(b.score AS DECIMAL) BETWEEN #{condition.minScore} AND #{condition.maxScore}
        </where>
        ORDER BY b.create_time ASC
        LIMIT 200
    </select>

    <select id="selectByComplexCondition" resultMap="QuestionBankResult">
        SELECT * FROM qh_question_bank b
        <where>
            del_flag = '0'
            <!-- 动态条件拼接 -->
            <if test="condition.questionType != null and condition.questionType != ''">
                AND b.question_type = #{condition.questionType}
            </if>
            <if test="condition.grades != null and condition.grades.size() > 0">
                AND b.grade_id IN
                <foreach item="grade" collection="condition.grades" open="(" separator="," close=")">
                    #{grade}
                </foreach>
            </if>
            <if test="condition.years != null and condition.years.size() > 0">
                AND b.year IN
                <foreach item="year" collection="condition.years" open="(" separator="," close=")">
                    #{year}
                </foreach>
            </if>
            <!-- 知识点条件 -->
            <if test="condition.knowledgeTreeIds != null and condition.knowledgeTreeIds.size() > 0">
                AND EXISTS (
                SELECT 1 FROM qh_knowledge_question kq
                WHERE kq.question_bank_id = b.id
                AND kq.knowledge_tree_id IN
                <foreach item="ktId" collection="condition.knowledgeTreeIds" open="(" separator="," close=")">
                    #{ktId}
                </foreach>
                )
            </if>
        </where>
    </select>
</mapper>
