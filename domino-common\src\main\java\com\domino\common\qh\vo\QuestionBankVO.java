package com.domino.common.qh.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 题库查询结果VO
 */
@Data
public class QuestionBankVO<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 总记录数
     */
    private long total;

    /**
     * 列表数据
     */
    private List<T> records;

    public QuestionBankVO() {}

    /**
     * 构造函数
     *
     * @param records 列表数据
     * @param total   总记录数
     */
    public QuestionBankVO(List<T> records, long total) {
        this.records = records;
        this.total = total;
    }
} 