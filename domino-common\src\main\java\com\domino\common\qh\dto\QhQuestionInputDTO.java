package com.domino.common.qh.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * 试卷信息
 *
 */

@Data
public class QhQuestionInputDTO implements Serializable {
    private static final long serialVersionUID = 3343110627967636507L;

    private String questionId;

    @NotBlank(message = "题目文本不能为空")
    private String questionText; //解方程：2x + 3 = 7

    private String gradeId;

    private String chapterId;

    @NotBlank(message = "难度不能为空")
    private String difficultyLevel;

    @NotBlank(message = "题目类型不能为空")
    private String questionType;  //计算题

    @NotBlank(message = "分数不能为空")
    private String score;

    @NotBlank(message = "年份不能为空")
    private String year;

    private String answerDocument;
    //base64格式图片
    private String answerImage;

    private String questionDocument;

    private String questionImage;

    private String knowledgePointId;

    private List<String> knowledgePointIDs;   //[1, 2]

    private String libraryId;

    private Boolean isFavorited;

    private List<String> Tags;  //方程,计算题,基础题

    private String paperType;  //专题卷

    private String paperRegion;  //江苏

    private String paperSource;  //2023年XX中学月考

    @NotBlank(message = "创建时间不能为空")
    private String createTime;  //2023-10-05 14:30:00

    private String analysisDocument;

    private String analysisImage;
}
