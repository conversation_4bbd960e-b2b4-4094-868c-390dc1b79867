package com.domino.qh.mapper;

import com.domino.common.qh.domain.QhExamPaper;
import com.domino.common.qh.domain.QhExamPaperQuestion;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface QhExamPaperMapper {

    int batchInsert(@Param("questionList") List<QhExamPaperQuestion> questionList);

    List<String> selectKnowledgeQuestionList(@Param("questionBankIds") List<String> questionBankIds);

    /**
     * 首页信息查询
     */
    Integer selectCount(@Param("beginTime") String beginTime, @Param("endTime") String endTime);

    /**
     * 查询试卷分类
     *
     * @param id 试卷分类主键
     * @return 试卷分类
     */
    QhExamPaper selectQhExamPaperById(String id);

    /**
     * 根据名称查询试卷
     *
     * @param paperName 名称
     * @param userId 用户id
     * @return 试卷
     */
    QhExamPaper selectQhExamPaperByPaperName(@Param("paperName") String paperName, @Param("userId") String userId);

    List<String> selectBankIds(String id);

    /**
     * 查询试卷分类列表
     *
     * @param qhExamPaper 试卷分类
     * @return 试卷分类集合
     */
    List<QhExamPaper> selectQhExamPaperList(QhExamPaper qhExamPaper);

    /**
     * 根据ID批量查询试卷
     *
     * @param idList 入参id
     * @return 试卷集合
     */
    List<QhExamPaper> selectBatchQhExamPaperList(@Param("idList") List<String> idList);

    /**
     * 新增试卷分类
     *
     * @param qhExamPaper 试卷分类
     * @return 结果
     */
    int insertQhExamPaper(QhExamPaper qhExamPaper);

    /**
     * 修改试卷分类
     *
     * @param qhExamPaper 试卷分类
     * @return 结果
     */
    int updateQhExamPaper(QhExamPaper qhExamPaper);

    /**
     * 删除试卷
     *
     * @param id 试卷主键
     * @return 结果
     */
    int deleteQhExamPaperById(String id);

    /**
     * 删除试卷
     *
     * @param id 试卷主键
     * @return 结果
     */
    int deleteQhGExamPaperById(String id);

    /**
     * 删除试卷关联题目
     *
     * @param id 试卷主键
     * @return 结果
     */
    int deleteQhExamPaperQuestionById(String id);

    /**
     * 批量删除试卷
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteQhExamPaperByIds(String[] ids);

    /**
     * 批量删除试卷
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteQhGExamPaperByIds(String[] ids);

    /**
     * 批量删除试卷关联题目
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteQhExamPaperQuestionByIds(String[] ids);
}
