package com.domino.common.lib;

import com.aspose.words.License;

import java.io.ByteArrayInputStream;

public class CrackApose {
    public static License license;
    public static void initLicense() {
        // 直接放入代码 String licenseFilePath = "excel-license.xml";
        if (null != license) {
            // 已经授权
            return;
        }
        try {
            StringBuffer buffer = new StringBuffer();
            buffer.append("<License>");
            buffer.append("  <Data>");
            buffer.append("    <Products>");
            buffer.append("      <Product>Aspose.Total for Java</Product>");
            buffer.append("      <Product>Aspose.Words for Java</Product>");
            buffer.append("    </Products>");
            buffer.append("    <EditionType>Enterprise</EditionType>");
            buffer.append("    <SubscriptionExpiry>20991231</SubscriptionExpiry>");
            buffer.append("    <LicenseExpiry>20991231</LicenseExpiry>");
            buffer.append("    <SerialNumber>8bfe198c-7f0c-4ef8-8ff0-acc3237bf0d7</SerialNumber>");
            buffer.append("  </Data>");
            buffer.append("  <Signature>sNLLKGMUdF0r8O1kKilWAGdgfs2BvJb/2Xp8p5iuDVfZXmhppo+d0Ran1P9TKdjV4ABwAgKXxJ3jcQTqE/2IRfqwnPf8itN8aFZlV3TJPYeD3yWE7IT55Gz6EijUpC7aKeoohTb4w2fpox58wWoF3SNp6sK6jDfiAUGEHYJ9pjU=</Signature>");
            buffer.append("</License>");
            ByteArrayInputStream byteArrayInputStream = null;
            byteArrayInputStream = new ByteArrayInputStream(buffer.toString().getBytes());
            license = new License();
            license.setLicense(byteArrayInputStream);
            byteArrayInputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
