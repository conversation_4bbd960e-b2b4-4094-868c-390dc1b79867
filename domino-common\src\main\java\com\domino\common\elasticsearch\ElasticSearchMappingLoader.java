package com.domino.common.elasticsearch;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * Elasticsearch映射配置加载器
 * 负责从配置类中加载ES映射配置并提供缓存机制
 */
@Slf4j
@Component
public class ElasticSearchMappingLoader {

    @Resource
    private ElasticSearchMappingConfig mappingConfig;

    private static final Map<String, String> MAPPING_CACHE = new HashMap<>();
    
    /**
     * 获取题目映射配置
     * @return 题目映射配置JSON字符串
     */
    public String getQuestionMapping() {
        // 先从缓存中获取
        if (MAPPING_CACHE.containsKey("question")) {
            return MAPPING_CACHE.get("question");
        }
        
        try {
            // 检查配置是否启用
            if (mappingConfig.getQuestion() == null || !mappingConfig.getQuestion().getEnabled()) {
                log.warn("题目映射配置未启用或不存在");
                return null;
            }
            
            // 检查是否有配置的字段
            if (mappingConfig.getQuestion().getProperties() == null || 
                mappingConfig.getQuestion().getProperties().isEmpty()) {
                log.warn("题目映射配置中没有定义字段属性");
                return null;
            }
            
            // 生成映射JSON
            String mapping = mappingConfig.getQuestion().toMappingJson();
            
            // 缓存映射配置
            MAPPING_CACHE.put("question", mapping);
            log.info("成功从配置类加载ES题目映射配置, 映射配置内容: {}", mapping);
            
            return mapping;
        } catch (Exception e) {
            log.error("从配置类加载ES映射配置失败", e);
            return null;
        }
    }
    
    /**
     * 获取索引名称
     * @return 索引名称
     */
    public String getQuestionIndexName() {
        if (mappingConfig.getQuestion() != null) {
            return mappingConfig.getQuestion().getIndexName();
        }
        return "qh_questions"; // 默认索引名称
    }
    
    /**
     * 检查题目映射配置是否启用
     * @return 是否启用
     */
    public boolean isQuestionMappingEnabled() {
        return mappingConfig.getQuestion() != null && mappingConfig.getQuestion().getEnabled() != null
                && mappingConfig.getQuestion().getEnabled();
    }
    
    /**
     * 清除缓存
     */
    public void clearCache() {
        MAPPING_CACHE.clear();
    }
    
    /**
     * 重新加载配置
     */
    public void reloadMapping() {
        clearCache();
        getQuestionMapping();
    }
}
