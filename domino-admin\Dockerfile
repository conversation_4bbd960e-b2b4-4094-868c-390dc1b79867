# 使用Eclipse Temurin官方镜像（推荐）
FROM eclipse-temurin:8-jre

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 设置元数据
LABEL maintainer="domino"

# 设置工作目录
WORKDIR /app

# 复制JAR文件
COPY target/domino-admin.jar app.jar

# 验证文件复制
RUN echo "JAR path: /app/app.jar" && ls -l

# 设置运行时环境变量（默认值，可通过docker run -e覆盖）
ENV REDIS_HOST="redis" \
    REDIS_PORT="6379" \
    REDIS_PWD="1qaz@WSX" \
    MINIO_HOST="*************" \
    MYSQL_HOST="mysql" \
    MYSQL_PORT="3306" \
    MYSQL_USR="root" \
    MYSQL_PWD="1qaz@WSX" \
    ZK_HOST="zookeeper" \
    ZK_PORT=2181

# 暴露应用端口
EXPOSE 8200

# 启动命令（直接使用工作目录路径）
ENTRYPOINT ["java", "-jar", "app.jar"]
