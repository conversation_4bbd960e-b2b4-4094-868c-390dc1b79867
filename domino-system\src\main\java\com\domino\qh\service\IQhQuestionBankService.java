package com.domino.qh.service;

import com.domino.common.core.page.TableDataInfo;
import com.domino.common.qh.domain.QhQuestionBank;
import com.domino.common.qh.dto.QhQuestionBoardOrder;
import com.domino.common.qh.form.PaperQuestionAnalysisForm;

import java.util.List;

/**
 * 题目 业务层
 */
public interface IQhQuestionBankService {
    /**
     * 根据条件分页查询题目列表
     *
     * @param questionBank 题目信息
     * @return 题目信息集合信息
     */
    public TableDataInfo selectQuestionBankList(QhQuestionBank questionBank);

    public List<QhQuestionBank> selectQuestionBankListExport(QhQuestionBank questionBank);

    /**
     * 首页信息查询
     */
    public Integer dashboard(QhQuestionBank questionBank);

    /**
     * 校验题目是否允许操作
     *
     * @param id 主键id
     */
    public void checkDataAllowed(String id);

    /**
     * 修改题目信息
     *
     * @param questionBank 题目信息
     * @return 结果
     */
    public int updateQuestionBank(QhQuestionBank questionBank);

    /**
     * 通过题目ID删除题目
     *
     * @param id 题目ID
     * @return 结果
     */
    public int deleteQuestionBankById(String id);

    /**
     * 添加试题
     *
     * @param form 试卷信息与试题西悉尼
     * @return 结果
     */
    public void addQuestionBank(PaperQuestionAnalysisForm form);

    /**
     * 批量删除题目信息
     *
     * @param ids 需要删除的题目ID
     * @return 结果
     */
    public int deleteQuestionBankByIds(List<String> ids);

    /**
     * 将试题加入试题栏
     *
     * @param id 需要加入试题栏的题目ID
     */
    public void addQuestionBoard(String type, String id);

    /**
     * 将试题从试题栏中删除
     *
     * @param id 需要从试题栏中删除的题目ID
     */
    public void removeQuestionBoard(String type, String id);

    /**
     * 获取用户的试题栏数据
     *
     * @return 结果
     */
    public List<QhQuestionBank> getQuestionBoard(String type);

    /**
     * 对试题栏中数据重新排序
     */
    public void orderQuestionBoard(String type, List<QhQuestionBoardOrder> orderList);
}
