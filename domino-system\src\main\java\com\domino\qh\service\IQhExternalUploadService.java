package com.domino.qh.service;

import com.domino.common.qh.dto.QhLibraryInputDTO;
import com.domino.common.qh.dto.QhPaperInputDTO;
import com.domino.common.qh.dto.QhQuestionInputDTO;

import java.util.List;

/**
 * 外部API-上传接口
 *
 */
public interface IQhExternalUploadService {

    //客户端基础数据-入库
    void inStorageByBase(List<QhLibraryInputDTO> inputDTOList);

    //客户端题目-入库
    void inStorageByQuestion(List<QhLibraryInputDTO> inputDTOList);

    //客户端题库-入库
    void inStorage(List<QhLibraryInputDTO> inputDTOList);

    //客户端试卷-入库
    void inStorageByPaper(List<QhLibraryInputDTO> inputDTOList);

}
