package com.domino.common.event;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.event.SmartApplicationListener;

/**
 * 抽象事件监听器基类
 * 提供统一的事件监听基础，不同业务可以继承此类实现具体的事件处理逻辑
 */
@Slf4j
public abstract class AbstractQhEventListener implements SmartApplicationListener {

    /**
     * 支持的事件类型，子类需要实现此方法指定具体支持的事件类型
     *
     * @param eventType 事件类型
     * @return 是否支持该事件类型
     */
    @Override
    public abstract boolean supportsEventType(Class<? extends ApplicationEvent> eventType);

    /**
     * 事件监听器的执行顺序，数值越小优先级越高
     * 子类可以重写此方法自定义执行顺序
     *
     * @return 执行顺序
     */
    @Override
    public int getOrder() {
        return 0;
    }

    /**
     * 事件处理入口方法
     * 提供统一的异常处理和日志记录，子类实现具体的业务处理逻辑
     *
     * @param event 应用事件
     */
    @Override
    public void onApplicationEvent(ApplicationEvent event) {
        try {
            // 记录事件处理开始日志
            //logEventStart(event);
            // 调用子类的具体处理方法
            handleEvent(event);
            // 记录事件处理成功日志
            //logEventSuccess(event);
        } catch (Exception e) {
            // 记录事件处理失败日志
            //logEventError(event, e);
            // 调用子类的异常处理方法
            handleEventError(event, e);
        }
    }

    /**
     * 具体的事件处理逻辑，子类必须实现此方法
     *
     * @param event 应用事件
     */
    protected abstract void handleEvent(ApplicationEvent event);

    /**
     * 事件处理异常时的处理逻辑，子类可以重写此方法自定义异常处理
     * 默认实现：重新抛出运行时异常
     *
     * @param event 应用事件
     * @param e 异常信息
     */
    protected void handleEventError(ApplicationEvent event, Exception e) {
        // 默认重新抛出异常，子类可以重写此方法实现自定义异常处理
        throw new RuntimeException("事件处理失败: " + getEventDescription(event), e);
    }

    /**
     * 记录事件处理开始日志
     *
     * @param event 应用事件
     */
    protected void logEventStart(ApplicationEvent event) {
        log.info("[{}] 开始处理事件: {}", getListenerName(), getEventDescription(event));
    }

    /**
     * 记录事件处理成功日志
     *
     * @param event 应用事件
     */
    protected void logEventSuccess(ApplicationEvent event) {
        log.info("[{}] 事件处理成功: {}", getListenerName(), getEventDescription(event));
    }

    /**
     * 记录事件处理失败日志
     *
     * @param event 应用事件
     * @param e 异常信息
     */
    protected void logEventError(ApplicationEvent event, Exception e) {
        log.error("[{}] 事件处理失败: {}, 错误信息: {}",
                getListenerName(), getEventDescription(event), e.getMessage(), e);
    }

    /**
     * 获取监听器名称，用于日志记录
     * 子类可以重写此方法自定义监听器名称
     *
     * @return 监听器名称
     */
    protected String getListenerName() {
        return this.getClass().getSimpleName();
    }

    /**
     * 获取事件描述信息，用于日志记录
     * 子类可以重写此方法自定义事件描述
     *
     * @param event 应用事件
     * @return 事件描述
     */
    protected String getEventDescription(ApplicationEvent event) {
        if (event == null) {
            return "null";
        }
        return event.getClass().getSimpleName() + "@" + event.getTimestamp();
    }

    /**
     * 检查事件是否为空
     *
     * @param event 应用事件
     * @return 是否为空
     */
    protected boolean isEventNull(ApplicationEvent event) {
        return event == null;
    }

    /**
     * 检查事件是否为指定类型
     *
     * @param event 应用事件
     * @param eventClass 事件类型
     * @return 是否为指定类型
     */
    protected boolean isEventType(ApplicationEvent event, Class<? extends ApplicationEvent> eventClass) {
        return event != null && eventClass.isAssignableFrom(event.getClass());
    }

    /**
     * 安全转换事件类型
     *
     * @param event 应用事件
     * @param eventClass 目标事件类型
     * @param <T> 事件类型泛型
     * @return 转换后的事件，如果转换失败返回null
     */
    @SuppressWarnings("unchecked")
    protected <T extends ApplicationEvent> T castEvent(ApplicationEvent event, Class<T> eventClass) {
        if (isEventType(event, eventClass)) {
            return (T) event;
        }
        return null;
    }

    /**
     * 获取当前时间戳
     *
     * @return 当前时间戳
     */
    protected long getCurrentTimestamp() {
        return System.currentTimeMillis();
    }

    /**
     * 计算事件处理耗时
     *
     * @param startTime 开始时间
     * @return 耗时（毫秒）
     */
    protected long calculateDuration(long startTime) {
        return getCurrentTimestamp() - startTime;
    }
}
