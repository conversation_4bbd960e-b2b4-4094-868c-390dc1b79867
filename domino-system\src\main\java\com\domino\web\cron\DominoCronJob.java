package com.domino.web.cron;

import com.domino.common.core.redis.RedisCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component("dominoCronJob")
public class DominoCronJob {

    @Resource
    private RedisCache redisCache;

    /**
     * 任务清除，每晚12点执行一次：0 0 12 * * ?
     */
    public void cleanUpTask() {
        // 获取任务结束队列中所有的key
        log.info("开始执行每晚12的任务清除定时任务");
        // redisCache.deleteObject(TaskQueue.TASK_END);
    }
}
