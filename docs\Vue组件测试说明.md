# Vue组件测试说明

## 试卷导出Vue组件测试指南

### 1. 组件文件位置
```
domino-admin/src/main/resources/static/vue/PaperExportDialog.vue
```

### 2. 测试环境准备

#### 2.1 在现有页面中集成测试

如果你已经有Vue项目，可以直接在现有页面中引入组件：

```vue
<template>
  <div>
    <!-- 触发导出的按钮 -->
    <el-button type="primary" @click="showExportDialog = true">
      导出试卷
    </el-button>
    
    <!-- 导出对话框组件 -->
    <PaperExportDialog
      :visible.sync="showExportDialog"
      :paper-id="currentPaperId"
      :paper-flag="currentPaperFlag"
    />
  </div>
</template>

<script>
import PaperExportDialog from './PaperExportDialog.vue';

export default {
  components: {
    PaperExportDialog
  },
  data() {
    return {
      showExportDialog: false,
      currentPaperId: 'test-paper-123',  // 替换为实际的试卷ID
      currentPaperFlag: 'ZJ'             // ZJ表示组卷，其他表示原生试卷
    };
  }
};
</script>
```

#### 2.2 创建独立测试页面

创建一个专门的测试页面 `test-export.html`：

```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>试卷导出测试</title>
    <!-- Element UI CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <!-- Vue.js -->
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <!-- Element UI JS -->
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <!-- Axios -->
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
</head>
<body>
    <div id="app">
        <el-container>
            <el-header>
                <h2>试卷导出功能测试</h2>
            </el-header>
            <el-main>
                <el-card>
                    <div slot="header">
                        <span>测试参数</span>
                    </div>
                    <el-form :inline="true">
                        <el-form-item label="试卷ID">
                            <el-input v-model="testPaperId" placeholder="请输入试卷ID"></el-input>
                        </el-form-item>
                        <el-form-item label="试卷标识">
                            <el-select v-model="testPaperFlag">
                                <el-option label="组卷(ZJ)" value="ZJ"></el-option>
                                <el-option label="原生试卷" value="NATIVE"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="showExportDialog = true">
                                打开导出对话框
                            </el-button>
                        </el-form-item>
                    </el-form>
                </el-card>
                
                <!-- 导出对话框 -->
                <paper-export-dialog
                    :visible.sync="showExportDialog"
                    :paper-id="testPaperId"
                    :paper-flag="testPaperFlag"
                />
            </el-main>
        </el-container>
    </div>

    <script>
        // 这里粘贴 PaperExportDialog.vue 的 script 部分内容
        Vue.component('paper-export-dialog', {
            // 组件定义...
        });

        new Vue({
            el: '#app',
            data: {
                showExportDialog: false,
                testPaperId: 'test-paper-123',
                testPaperFlag: 'ZJ'
            }
        });
    </script>
</body>
</html>
```

### 3. 测试步骤

#### 3.1 基本功能测试

1. **打开对话框**
   - 点击"导出试卷"按钮
   - 验证对话框正常弹出
   - 检查默认选项是否正确

2. **选择导出格式**
   - 测试PDF和Word格式选择
   - 验证单选框正常工作

3. **选择导出内容**
   - 测试"试卷题目"、"题目答案"、"题目解析"复选框
   - 验证至少选择一项的校验

4. **设置格式选项**
   - 测试页面大小选择（A4、A3、Letter）
   - 测试字体大小调整（8-24）
   - 测试页边距设置（10-50mm）
   - 测试其他选项的勾选

#### 3.2 导出功能测试

1. **PDF导出测试**
   ```javascript
   // 在浏览器控制台执行
   exportPaperToPdf('test-paper-123', {
       includeQuestions: true,
       includeAnswers: false,
       includeAnalysis: false
   });
   ```

2. **Word导出测试**
   ```javascript
   // 在浏览器控制台执行
   exportPaperToWord('test-paper-123', {
       includeQuestions: true,
       includeAnswers: true,
       includeAnalysis: true
   });
   ```

#### 3.3 错误处理测试

1. **无效试卷ID测试**
   - 输入不存在的试卷ID
   - 验证错误提示是否正确显示

2. **网络错误测试**
   - 断开网络连接
   - 测试导出时的错误处理

3. **服务器错误测试**
   - 模拟服务器500错误
   - 验证错误信息显示

### 4. 调试技巧

#### 4.1 浏览器开发者工具

1. **Network面板**
   - 查看导出请求是否正确发送
   - 检查请求参数和响应状态
   - 验证文件下载是否成功

2. **Console面板**
   - 查看JavaScript错误信息
   - 使用console.log调试组件状态

#### 4.2 Vue Devtools

如果安装了Vue Devtools扩展：
- 查看组件的data状态
- 监控props传递
- 检查事件触发

#### 4.3 常见问题排查

1. **组件不显示**
   - 检查组件是否正确注册
   - 验证visible属性绑定
   - 查看CSS样式是否冲突

2. **导出无响应**
   - 检查API接口是否正确
   - 验证请求参数格式
   - 查看后端日志

3. **文件下载失败**
   - 检查浏览器下载设置
   - 验证文件MIME类型
   - 查看响应头设置

### 5. 测试用例

#### 5.1 正常流程测试用例

| 测试项 | 操作步骤 | 预期结果 |
|--------|----------|----------|
| 打开对话框 | 点击导出按钮 | 对话框正常弹出，显示默认选项 |
| 选择PDF格式 | 选择PDF单选框 | 格式正确切换 |
| 选择导出内容 | 勾选试卷+答案 | 复选框状态正确 |
| 设置格式 | 修改字体大小为14 | 数值正确更新 |
| 执行导出 | 点击开始导出 | 文件成功下载 |

#### 5.2 异常情况测试用例

| 测试项 | 操作步骤 | 预期结果 |
|--------|----------|----------|
| 空试卷ID | 清空试卷ID后导出 | 显示错误提示 |
| 未选择内容 | 取消所有内容选项 | 按钮禁用或提示 |
| 网络断开 | 断网后执行导出 | 显示网络错误提示 |

### 6. 性能测试

1. **大文件导出**
   - 测试包含大量题目的试卷
   - 验证导出时间和内存使用

2. **并发导出**
   - 同时打开多个导出对话框
   - 测试系统稳定性

### 7. 浏览器兼容性测试

测试以下浏览器：
- Chrome (推荐)
- Firefox
- Safari
- Edge

### 8. 移动端测试

如果需要支持移动端：
- 测试响应式布局
- 验证触摸操作
- 检查文件下载功能

通过以上测试步骤，可以全面验证Vue导出组件的功能和稳定性。
