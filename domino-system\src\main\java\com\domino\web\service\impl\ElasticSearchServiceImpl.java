package com.domino.web.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.domino.common.elasticsearch.ElasticSearchUtil;
import com.domino.web.form.ElasticSearchForm;
import com.domino.web.service.ElasticSearchService;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ElasticSearchServiceImpl implements ElasticSearchService {

    @Resource
    private ElasticSearchUtil elasticSearchUtil;

    @Override
    public List<Map<String, Object>> getList(ElasticSearchForm form) {
        try {
            if (StrUtil.isBlank(form.getIndex())) {
                throw new RuntimeException("查询索引为空");
            }

            // 加载com.domino.web.domain目录下的类
            Class<?> aClass = Class.forName("com.domino.web.domain." + form.getIndex());
            // 获取类的所有字段
            List<String> allFields = Arrays.stream(aClass.getDeclaredFields())
                    .map(Field::getName)
                    .collect(Collectors.toList());

            // 判断排序字段和高亮字段
            if (!CollUtil.containsAll(allFields, form.getSortField()) || !CollUtil.containsAll(allFields, form.getHighlightField())) {
                throw new RuntimeException("查询字段有误！");
            }

            // 构建查询条件
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();

            if (CollUtil.isEmpty(form.getSearchConditions()) || form.getSearchConditions().entrySet().stream().allMatch(entry -> StrUtil.isBlank(entry.getValue()))) {
                // 如果没有条件，则查询所有
                sourceBuilder.query(QueryBuilders.matchAllQuery());
            } else {
                // 构建boolean查询
                BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
                form.getSearchConditions().forEach((key, value) -> {
                    if (!allFields.contains(key)) {
                        throw new RuntimeException("查询字段有误！");
                    }
                    boolQuery.must(QueryBuilders.matchQuery(key, value));
                });
                sourceBuilder.query(boolQuery);
            }

            // 查询字段
            String searchFields = allFields.stream().filter(fieldName -> !"createTime".equals(fieldName) && !"updateTime".equals(fieldName) && !"isDelete".equals(fieldName)).collect(Collectors.joining(","));
            // 排序字段
            String sortFields = String.join(",", form.getSortField());
            // 高亮字段
            String highLightFields = String.join(",", form.getHighlightField());

            // 去ES中查询，在ES中，index必须小写
            return elasticSearchUtil.searchListData(form.getIndex().toLowerCase(),
                    sourceBuilder,
                    form.getPageSize(),
                    form.getPageNum(),
                    searchFields,
                    sortFields,
                    highLightFields);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void deleteWordById(String id) {
        try {
            elasticSearchUtil.deleteDataById("word", id);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void updateWordById(String id) {
        // try {
        //     Word word = wordMapper.selectById(id);
        //     // 判空
        //     if (Objects.isNull(word)) {
        //         log.error("单词: {} 不存在", id);
        //         return;
        //     }
        //     if (elasticSearchUtil.existsById("word", word.getId())) {
        //         // 如果es中存在则更新
        //         elasticSearchUtil.updateDataById(word, "word", word.getId());
        //     } else {
        //         // 如果es中不存在则新增
        //         elasticSearchUtil.addData("word", JSONObject.parseObject(JSON.toJSONString(word)), word.getId());
        //     }
        // } catch (IOException e) {
        //     throw new RuntimeException(e);
        // }
    }
}
