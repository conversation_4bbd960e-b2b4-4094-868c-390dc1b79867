package com.domino.flinkcdc;

import com.alibaba.fastjson.JSONObject;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;

/**
 * 处理question_bank表的变更数据
 * 解析不同类型的变更（新增、更新、删除）并进行相应处理
 */
public class QuestionBankProcessFunction extends ProcessFunction<String, String> {

    @Override
    public void processElement(
            String jsonString,
            ProcessFunction<String, String>.Context context,
            Collector<String> collector) {

        // 解析JSON字符串
        JSONObject jsonObject = JSONObject.parseObject(jsonString);

        // 获取操作类型：c(create-新增), u(update-更新), d(delete-删除)
        String op = jsonObject.getString("op");

        // 防止op为null的情况
        if (op == null) {
            System.out.println("未知的操作类型，原始数据: " + jsonString);
            collector.collect(jsonString);
            return;
        }

        // 获取数据
        JSONObject data = jsonObject.getJSONObject("data");
        // 更新操作时的旧数据
        JSONObject oldData = jsonObject.getJSONObject("old");

        // 根据操作类型处理不同的变更
        switch (op) {
            case "c":
                handleCreate(data, jsonString);
                break;
            case "u":
                handleUpdate(oldData, data, jsonString);
                break;
            case "d":
                // 处理删除操作，优先使用oldData，如果oldData也为null则使用原始数据
                handleDelete(data != null ? data : oldData, jsonString);
                break;
            // 初始快照读取时的操作类型
            case "r":
                handleRead(data, jsonString);
                break;
            default:
                System.out.println("未知操作类型: " + op + "，原始数据: " + jsonString);
        }

        // 将处理结果发送到下一个处理节点
        collector.collect(jsonString);
    }

    /**
     * 处理新增操作
     * @param data 新增的数据
     * @param originalData 原始JSON数据，用于调试
     */
    private void handleCreate(JSONObject data, String originalData) {
        if (data == null) {
            System.out.println("新增操作数据为空，原始数据: " + originalData);
            return;
        }

        System.out.println("===== 检测到新题目 =====");
        System.out.println("题目ID: " + data.getString("id"));
        System.out.println("题目内容: " + data.getString("content"));
        // 这里可以添加你的业务逻辑，比如发送通知、进行分析等
    }

    /**
     * 处理更新操作
     * @param oldData 更新前的数据
     * @param newData 更新后的数据
     * @param originalData 原始JSON数据，用于调试
     */
    private void handleUpdate(JSONObject oldData, JSONObject newData, String originalData) {
        if (oldData == null || newData == null) {
            System.out.println("更新操作数据不完整，原始数据: " + originalData);
            return;
        }

        System.out.println("===== 检测到题目更新 =====");
        System.out.println("题目ID: " + newData.getString("id"));
        System.out.println("更新前内容: " + oldData.getString("content"));
        System.out.println("更新后内容: " + newData.getString("content"));
        // 这里可以添加你的业务逻辑
    }

    /**
     * 处理删除操作
     * @param data 被删除的数据（可能是data或oldData）
     * @param originalData 原始JSON数据，用于调试
     */
    private void handleDelete(JSONObject data, String originalData) {
        if (data == null) {
            System.out.println("删除操作数据为空，原始数据: " + originalData);
            return;
        }

        System.out.println("===== 检测到题目删除 =====");
        System.out.println("被删除的题目ID: " + data.getString("id"));
        System.out.println("被删除的题目内容: " + data.getString("content"));
        // 这里可以添加你的业务逻辑
    }

    /**
     * 处理初始读取操作（全量同步时）
     * @param data 读取到的数据
     * @param originalData 原始JSON数据，用于调试
     */
    private void handleRead(JSONObject data, String originalData) {
        if (data == null) {
            System.out.println("初始加载数据为空，原始数据: " + originalData);
            return;
        }

        System.out.println("===== 初始加载题目 =====");
        System.out.println("题目ID: " + data.getString("id"));
        System.out.println("题目内容: " + data.getString("content"));
        // 这里可以添加初始加载时的业务逻辑
    }
}

