-- 任务审核通过逻辑
-- KEYS[1]: task_wait_audit
-- KEYS[2]: task_wait_start
-- KEYS[3]: task_user_query_new
-- KEYS[4]: task_user_query_old
-- ARGV[1]: taskId

-- 从task_wait_audit这个Set集合中移除taskId
redis.call('SREM', KEYS[1], ARGV[1])

-- 添加taskId到task_wait_start这个Set集合中
redis.call('SADD', KEYS[2], ARGV[1])

-- 更新用户查询缓存
if redis.call('EXISTS', KEYS[3]) == 1 then
    -- 如果task_user_query_new存在，则删除旧的缓存并重命名新的缓存
    redis.call('DEL', KEYS[4])
    redis.call('RENAME', KEYS[3], KEYS[4])
end
-- 脚本执行成功，无需返回特定值（或者可以返回1表示成功）
