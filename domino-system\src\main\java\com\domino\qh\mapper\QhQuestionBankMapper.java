package com.domino.qh.mapper;

import com.domino.common.qh.domain.QhQuestionBank;
import com.domino.common.qh.dto.QueryCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 试题表 数据层
 */
@Mapper
public interface QhQuestionBankMapper {

    /**
     * 根据条件分页查询试题列表
     *
     * @param questionBank 试题信息
     * @return 试题信息集合信息
     */
    List<QhQuestionBank> selectQuestionBankList(QhQuestionBank questionBank);

    /**
     * 首页信息查询
     */
    public Integer selectCount(QhQuestionBank questionBank);

    /**
     * 通过试题ID查询试题
     *
     * @param id 试题ID
     * @return 试题对象信息
     */
    QhQuestionBank selectQuestionBankById(String id);

    /**
     * 通过试题ID批量查询试题
     *
     * @param ids 试题ID集合
     * @return 试题对象信息
     */
    List<QhQuestionBank> selectQuestionBankBatchByIds(@Param("ids") List<String> ids);

    /**
     * 通过试题ID批量查询试题，并按照试题id排序
     *
     * @param ids 试题ID集合
     * @return 试题对象信息
     */
    List<QhQuestionBank> selectQuestionBankBatchByIdsOrderByIds(@Param("ids") List<String> ids);

    /**
     * 新增试题信息
     *
     * @param questionBank 试题信息
     * @return 结果
     */
    int insertQuestionBank(QhQuestionBank questionBank);

    /**
     * 修改试题信息
     *
     * @param questionBank 试题信息
     * @return 结果
     */
    int updateQuestionBank(QhQuestionBank questionBank);

    /**
     * 通过试题ID删除试题（逻辑删除）
     *
     * @param qhQuestionBank 试题ID
     * @return 结果
     */
    int deleteQuestionBankById(QhQuestionBank qhQuestionBank);

    /**
     * 通过试卷ID删除试题（逻辑删除）
     *
     * @param paperId 试卷ID
     * @return 结果
     */
    int deleteQuestionBankByPaperId(@Param("paperId") String paperId,@Param("createBy") String userId);

    /**
     * 批量删除试题信息（物理删除）
     *
     * @param ids      需要删除的试题ID
     * @param username 用户名称
     * @return 结果
     */
    int deleteQuestionBankByIds(@Param("ids") List<String> ids, @Param("createBy") String username);

    /**
     * 查询题目
     *
     * @param bank 试题信息
     * @return 试题信息集合信息
     */
    List<QhQuestionBank> selectBankList(@Param("bank") QhQuestionBank bank);

    /**
     * 查询题目
     */
    List<QhQuestionBank> selectAll();

    List<QhQuestionBank> selectByCondition(@Param("condition") QueryCondition condition);

    List<QhQuestionBank> selectByComplexCondition(@Param("condition") QueryCondition condition);
}
