-- 任务从待开始进入运行状态
-- KEYS[1]: task_wait_start
-- KEYS[2]: task_running
-- KEYS[3]: task_running_scramble
-- KEYS[4]: task_user_query_new
-- KEYS[5]: task_user_query_old
-- ARGV[1]: taskId
-- ARGV[2]: taskNum

-- 检查taskId是否在task_wait_start中，避免不必要的操作
if redis.call('SISMEMBER', KEYS[1], ARGV[1]) == 1 then
    -- 把任务移入进行中的队列
    redis.call('SREM', KEYS[1], ARGV[1])
    redis.call('SADD', KEYS[2], ARGV[1])

    -- 用Hash类型存储任务的数量
    redis.call('HSET', KEYS[3], ARGV[1], ARGV[2])

    -- 更新用户查询缓存
    if redis.call('EXISTS', KEYS[4]) == 1 then
        redis.call('DEL', KEYS[5])  -- 先删除旧的缓存，避免RENAME失败（如果旧缓存被其他客户端占用）
        redis.call('RENAME', KEYS[4], KEYS[5])
    end
end
-- 脚本执行成功，无需返回特定值（或者可以返回1表示成功）
