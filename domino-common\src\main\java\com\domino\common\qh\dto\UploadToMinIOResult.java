package com.domino.common.qh.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UploadToMinIOResult implements Serializable {

    private String base64Image;

    private String objectName;

    private boolean success;

    private String errorMessage;

    public UploadToMinIOResult(String base64Image, String objectName, boolean success) {
        this.base64Image = base64Image;
        this.objectName = objectName;
        this.success = success;
    }
}
