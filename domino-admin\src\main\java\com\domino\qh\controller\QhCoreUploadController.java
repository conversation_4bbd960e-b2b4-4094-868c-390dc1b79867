package com.domino.qh.controller;

import com.domino.common.core.controller.BaseController;
import com.domino.common.core.domain.AjaxResult;
import com.domino.common.core.redis.RedisCache;
import com.domino.common.utils.SecurityUtils;
import com.domino.qh.service.impl.PaperProcessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * 录题
 */
@RestController()
@RequestMapping("/qh/upload")
public class QhCoreUploadController extends BaseController {


    @Autowired
    private PaperProcessService paperProcessService;
    @Resource
    private RedisCache redisCache;

    @PostMapping("/uploadPaper")
    public void uploadPaper(@RequestParam("file") MultipartFile file) throws Exception {
        // 异步解析
        paperProcessService.processPaper(SecurityUtils.getUserId(), file);
    }

    /**
     * 查询试卷解析情况
     */
    @GetMapping("/status")
    public AjaxResult status() {
        return success(paperProcessService.processStatus());
    }

    /**
     * 删除某个试卷的解析解析以及试卷
     */
    @GetMapping("/delete")
    public AjaxResult delete(@RequestParam String fileName) {
        paperProcessService.delete(fileName);
        return success();
    }

    /**
     * 查询试卷解析结果
     */
    @GetMapping("/result")
    public AjaxResult result(@RequestParam String fileName) {
        String resultKey = "processPaper:" + SecurityUtils.getUserId() + ":" + fileName;
        return success(redisCache.getCacheMap(resultKey).get("result"));
    }
}
