package com.domino.qh.controller;

import com.domino.common.annotation.Log;
import com.domino.common.constant.HttpStatus;
import com.domino.common.core.controller.BaseController;
import com.domino.common.core.domain.AjaxResult;
import com.domino.common.core.page.TableDataInfo;
import com.domino.common.enums.BusinessType;
import com.domino.common.qh.domain.QhQuestionBank;
import com.domino.common.qh.dto.QhQuestionBoardOrder;
import com.domino.common.qh.form.PaperQuestionAnalysisForm;
import com.domino.common.utils.poi.ExcelUtil;
import com.domino.qh.service.IQhQuestionBankService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 题目信息
 */
@RestController
public class QhQuestionBankController extends BaseController {

    @Resource
    private IQhQuestionBankService questionBankService;

    /**
     * 获取题目列表
     */
    @PreAuthorize("@ss.hasPermi('qh:questionBank:query')")
    @GetMapping("/qh/questionBank/list")
    public TableDataInfo list(QhQuestionBank questionBank) {
        // 查询试题信息
        TableDataInfo tableDataInfo = questionBankService.selectQuestionBankList(questionBank);
        tableDataInfo.setCode(HttpStatus.SUCCESS);
        tableDataInfo.setMsg("查询成功");
        return tableDataInfo;
    }

    /**
     * 首页统计数据
     */
    @PreAuthorize("@ss.hasPermi('system:user:list')")
    @GetMapping("/qh/questionBank/dashboard")
    public AjaxResult dashboard(QhQuestionBank questionBank) {
        return success(questionBankService.dashboard(questionBank));
    }

    @Log(title = "题目管理", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('qh:questionBank:query')")
    @PostMapping("/qh/questionBank/export")
    public void export(HttpServletResponse response, QhQuestionBank questionBank) {
        List<QhQuestionBank> list = questionBankService.selectQuestionBankListExport(questionBank);
        ExcelUtil<QhQuestionBank> util = new ExcelUtil<>(QhQuestionBank.class);
        util.exportExcel(response, list, "题目数据");
    }

    /**
     * 上传解析，上传解析功能
     */
    @PreAuthorize("@ss.hasPermi('qh:questionBank:update')")
    @Log(title = "题目管理", businessType = BusinessType.UPDATE)
    @PutMapping("/qh/questionBank")
    public AjaxResult edit(@Validated @RequestBody QhQuestionBank questionBank) {
        // 更新关联表，暂时不需要，暂时只有更新解析
        questionBankService.checkDataAllowed(questionBank.getId());
        return toAjax(questionBankService.updateQuestionBank(questionBank));
    }

    /**
     * 删除题目
     */
    @PreAuthorize("@ss.hasPermi('qh:questionBank:update')")
    @Log(title = "题目管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/qh/questionBank/{id}")
    public AjaxResult remove(@PathVariable String id) {
        questionBankService.checkDataAllowed(id);
        return toAjax(questionBankService.deleteQuestionBankById(id));
    }

    /**
     * 添加题目
     */
    @PreAuthorize("@ss.hasPermi('qh:questionBank:add')")
    @PostMapping("/qh/questionBank")
    public AjaxResult add(@RequestBody PaperQuestionAnalysisForm form) {
        questionBankService.addQuestionBank(form);
        return success();
    }

    /**
     * 将试题加入试题栏
     */
    @PreAuthorize("@ss.hasPermi('qh:questionBank:board')")
    @PutMapping("/qh/questionBank/board/{type}/{id}")
    public AjaxResult addQuestionBoard(@PathVariable String type, @PathVariable String id) {
        questionBankService.addQuestionBoard(type,id);
        return success("试题栏加入成功！");
    }

    /**
     * 将试题从试题栏中删除
     */
    @PreAuthorize("@ss.hasPermi('qh:questionBank:board')")
    @DeleteMapping("/qh/questionBank/board/{type}/{id}")
    public AjaxResult removeQuestionBoard(@PathVariable String type, @PathVariable String id) {
        questionBankService.removeQuestionBoard(type,id);
        return success("试题栏删除成功！");
    }

    /**
     * 获取试题栏
     */
    @PreAuthorize("@ss.hasPermi('qh:questionBank:board')")
    @GetMapping("/qh/questionBank/board/{type}")
    public AjaxResult getQuestionBoard(@PathVariable String type) {
        return AjaxResult.success(type,questionBankService.getQuestionBoard(type));
    }

    /**
     * 试题栏重排序
     */
    @PreAuthorize("@ss.hasPermi('qh:questionBank:board')")
    @PostMapping("/qh/questionBank/board/order/{type}")
    public AjaxResult orderQuestionBoard(@PathVariable String type, @Valid @RequestBody List<QhQuestionBoardOrder> orderList) {
        questionBankService.orderQuestionBoard(type,orderList);
        return success();
    }
}
