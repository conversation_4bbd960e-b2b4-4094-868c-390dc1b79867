package com.domino.common.event;

/**
 * 题目事件类型枚举
 */
public enum QuestionEventType implements EventType {
    
    /**
     * 新增题目
     */
    INSERT("INSERT", "新增题目"),
    
    /**
     * 更新题目
     */
    UPDATE("UPDATE", "更新题目"),
    
    /**
     * 删除题目
     */
    DELETE("DELETE", "删除题目"),
    
    /**
     * 批量删除题目
     */
    BATCH_DELETE("BATCH_DELETE", "批量删除题目"),
    
    /**
     * 批量新增题目
     */
    BATCH_INSERT("BATCH_INSERT", "批量新增题目"),
    
    /**
     * 重建索引
     */
    REBUILD_INDEX("REBUILD_INDEX", "重建索引"),
    
    /**
     * 同步数据
     */
    SYNC_DATA("SYNC_DATA", "同步数据");
    
    private final String code;
    private final String description;
    
    QuestionEventType(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    @Override
    public String toString() {
        return code + "(" + description + ")";
    }
}
