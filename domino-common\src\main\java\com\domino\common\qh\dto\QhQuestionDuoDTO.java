package com.domino.common.qh.dto;

import lombok.Data;

import java.util.List;

@Data
public class QhQuestionDuoDTO {

    private String id;

    private String questionType; // 题型

    private String difficulty;   // 难度

    private String score; // 分值

    private Integer requiredNumber; // 题目数量

    private String region; //地区

    private List<String> gradeIds;    //年级列表

    private List<String> paperTypes;  //试卷类型列表

    private List<String> years; //年份列表

    private List<String> chapters;    //章节ID列表

    private List<String> knowledgePoints; // 知识点ID列表

    private List<String> tags; // 标签

}
