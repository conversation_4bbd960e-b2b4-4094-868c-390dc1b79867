package com.domino.qh.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.domino.common.constant.QhConstants;
import com.domino.common.core.domain.entity.SysUser;
import com.domino.common.core.page.TableDataInfo;
import com.domino.common.core.redis.RedisCache;
import com.domino.common.enums.QhPaperProcessType;
import com.domino.common.event.QuestionEventPublisher;
import com.domino.common.exception.qh.QhException;
import com.domino.common.qh.document.QuestionDocument;
import com.domino.common.qh.document.QuestionSearchBuilder;
import com.domino.common.qh.domain.QhExamPaper;
import com.domino.common.qh.domain.QhKnowledgeQuestion;
import com.domino.common.qh.domain.QhKnowledgeTree;
import com.domino.common.qh.domain.QhQuestionBank;
import com.domino.common.qh.dto.QhProcessPaperDTO;
import com.domino.common.qh.dto.QhProcessPaperDetailDTO;
import com.domino.common.qh.dto.QhQuestionBoardOrder;
import com.domino.common.qh.form.PaperAnalysisForm;
import com.domino.common.qh.form.PaperQuestionAnalysisForm;
import com.domino.common.qh.form.QuestionAnalysisForm;
import com.domino.common.qh.form.QuestionSearchForm;
import com.domino.common.qh.vo.QuestionBankVO;
import com.domino.common.utils.DateUtils;
import com.domino.common.utils.SecurityUtils;
import com.domino.common.utils.StringUtils;
import com.domino.common.utils.uuid.IdUtils;
import com.domino.qh.mapper.QhExamPaperMapper;
import com.domino.qh.mapper.QhKnowledgeQuestionMapper;
import com.domino.qh.mapper.QhKnowledgeTreeMapper;
import com.domino.qh.mapper.QhQuestionBankMapper;
import com.domino.qh.service.IQhKnowledgeTreeService;
import com.domino.qh.service.IQhQuestionBankService;
import com.domino.qh.service.QuestionElasticSearchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.domino.common.utils.SecurityUtils.getUsername;

/**
 * 题目 业务层处理
 */
@Service
@Slf4j
public class QuestionBankServiceImpl implements IQhQuestionBankService {

    @Resource
    private QhQuestionBankMapper questionBankMapper;
    @Resource
    private QhKnowledgeQuestionMapper knowledgeQuestionMapper;
    @Resource
    private QhKnowledgeTreeMapper knowledgeTreeMapper;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private QhExamPaperMapper qhExamPaperMapper;
    @Resource
    private RedisCache redisCache;
    @Resource
    private QuestionElasticSearchService questionElasticSearchService;
    @Resource
    private IQhKnowledgeTreeService knowledgeTreeService;
    @Resource
    private QuestionEventPublisher questionEventPublisher;

    /**
     * 根据条件分页查询题目列表
     *
     * @param questionBank 题目信息
     * @return 题目信息集合信息
     */
    @Override
    public TableDataInfo selectQuestionBankList(QhQuestionBank questionBank) {
        TableDataInfo tableDataInfo = new TableDataInfo();
        List<String> knowledgeTreeIds = questionBank.getKnowledgeTreeIds();

        // 如果未指定某个题库，则查询所有题库
        if (CollectionUtils.isEmpty(knowledgeTreeIds)) {
            LambdaQueryWrapper<QhKnowledgeTree> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(QhKnowledgeTree::getParentId, "0")
                    .eq(QhKnowledgeTree::getDelFlag, 0);
            if (!"admin".equals(getUsername())) {
                wrapper.nested(queryWrapper -> {
                    wrapper.eq(QhKnowledgeTree::getCreateBy, getUsername());
                    wrapper.or().eq(QhKnowledgeTree::getShareType, "1");
                });
            }

            knowledgeTreeIds = knowledgeTreeMapper.selectList(wrapper)
                    .stream()
                    .map(QhKnowledgeTree::getId)
                    .collect(Collectors.toList());
        }

        // 过滤可访问节点并检查结果
        List<String> filteredIds = filterAccessible(knowledgeTreeIds);
        if (CollectionUtils.isEmpty(filteredIds)) {
            tableDataInfo.setRows(Collections.emptyList());
            return tableDataInfo;
        }

        // 合并父节点和子节点ID：使用Set避免重复
        Set<String> allIds = new LinkedHashSet<>(filteredIds);
        filteredIds.stream()
                .flatMap(id -> knowledgeTreeMapper.selectChildrenKnowledgeById(id).stream())
                .map(QhKnowledgeTree::getId)
                .forEach(allIds::add);

        // 使用合并后的ID查询ES
        questionBank.setKnowledgeTreeIds(new ArrayList<>(allIds));
        QuestionBankVO<QhQuestionBank> questionBankVO = searchQuestionsFromES(questionBank);
        if (ObjectUtil.isNotNull(questionBankVO) && !CollectionUtils.isEmpty(questionBankVO.getRecords())) {
            // 填充试题信息，如是否在试题栏、是否在导出栏
            fillKnowledgeTreeData(questionBankVO.getRecords());
            fillBoardData(questionBankVO.getRecords());
            tableDataInfo.setRows(questionBankVO.getRecords());
            tableDataInfo.setTotal(questionBankVO.getTotal());
            return tableDataInfo;
        }
        tableDataInfo.setRows(Collections.emptyList());
        return tableDataInfo;
    }

    @Override
    public List<QhQuestionBank> selectQuestionBankListExport(QhQuestionBank questionBank) {
        // 过滤掉不合法的节点
        List<String> knowledgeTreeIds = filterAccessible(questionBank.getKnowledgeTreeIds());
        // 如果没有查询任何一个节点，则方法直接结束
        if (CollectionUtils.isEmpty(knowledgeTreeIds)) {
            return Collections.emptyList();
        }
        questionBank.setKnowledgeTreeIds(knowledgeTreeIds);
        // 查询结果
        List<QhQuestionBank> qhQuestionBankList = questionBankMapper.selectQuestionBankList(questionBank);
        // 填充节点信息
        fillKnowledgeTreeData(qhQuestionBankList);
        return qhQuestionBankList;
    }

    /**
     * 过滤不合法的节点
     */
    private List<String> filterAccessible(List<String> knowledgeTreeIds) {
        // 查询题目，必须根据某个节点去查询
        if (CollectionUtils.isEmpty(knowledgeTreeIds)) {
            return Collections.emptyList();
        }

        // 校验查询的节点是否有不合法的节点
        return knowledgeTreeIds.stream().filter(knowledgeTreeId -> {
            QhKnowledgeTree qhKnowledgeTree = knowledgeTreeMapper.selectKnowledgeById(knowledgeTreeId);
            return Objects.nonNull(qhKnowledgeTree) && qhKnowledgeTree.isAccessibleQuery();
        }).collect(Collectors.toList());
    }

    /**
     * 填充知识点信息
     */
    private void fillKnowledgeTreeData(List<QhQuestionBank> questionBankList) {
        // 填充节点信息
        questionBankList.forEach(qhQuestionBank -> {
            // 填充试卷信息
            if (StringUtils.hasText(qhQuestionBank.getSourcePaper())) {
                QhExamPaper qhExamPaper = qhExamPaperMapper.selectQhExamPaperById(qhQuestionBank.getSourcePaper());
                if (Objects.nonNull(qhExamPaper)) {
                    qhQuestionBank.setYear(qhExamPaper.getPyear());
                    qhQuestionBank.setRegion(qhExamPaper.getRegion());
                    qhQuestionBank.setSourcePaper(qhExamPaper.getPaperName());
                }
            }

            // 判断当前题目是否被加入试题栏
            String key = QhConstants.QUESTION_BOARD + SecurityUtils.getUserId();
            Double score = stringRedisTemplate.opsForZSet().score(key, qhQuestionBank.getId());
            qhQuestionBank.setInBoard(Objects.nonNull(score));

            // 判断当前题目是否被加入导出栏
            key = QhConstants.QUESTION_EXPORT + SecurityUtils.getUserId();
            score = stringRedisTemplate.opsForZSet().score(key, qhQuestionBank.getId());
            qhQuestionBank.setInExport(Objects.nonNull(score));
        });
    }

    private void fillBoardData(List<QhQuestionBank> questionBankList) {
        // 填充节点信息
        List<String> ids = questionBankList.stream().map(QhQuestionBank::getId).collect(Collectors.toList());
        List<QhKnowledgeQuestion> questionList = knowledgeQuestionMapper.batchSelectBankByKids(null, ids);
        if (CollectionUtils.isEmpty(questionList)) {
            return;
        }
        Map<String, List<QhKnowledgeQuestion>> questionMap = questionList.stream().collect(Collectors.groupingBy(QhKnowledgeQuestion::getQuestionBankId));
        questionBankList.forEach(qhQuestionBank -> {
            // 汇总节点id
            List<String> knowledgeTreeIds = questionMap.get(qhQuestionBank.getId()).stream().map(QhKnowledgeQuestion::getKnowledgeTreeId).collect(Collectors.toList());
            // 根据节点id查询具体节点
            List<QhKnowledgeTree> qhKnowledgeTrees = knowledgeTreeMapper.selectKnowledgeByIds(knowledgeTreeIds);
            // 设置节点信息
            qhQuestionBank.setKnowledgeTreeList(qhKnowledgeTrees);
        });
    }

    @Override
    public Integer dashboard(QhQuestionBank questionBank) {
        return questionBankMapper.selectCount(questionBank);
    }

    /**
     * 校验题目是否允许操作
     *
     * @param id 主键id
     */
    @Override
    public void checkDataAllowed(String id) {
        // 除了admin试题以外，只允许修改或删除自己创建的数据
        if (!SysUser.isAdmin(SecurityUtils.getUserId()) && StringUtils.hasText(id)) {
            QhQuestionBank qhQuestionBank = questionBankMapper.selectQuestionBankById(id);
            if (!qhQuestionBank.getCreateBy().equals(getUsername())) {
                throw new RuntimeException("无法删除非本人数据！");
            }
        }
    }

    /**
     * 修改保存题目信息
     *
     * @param questionBank 题目信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateQuestionBank(QhQuestionBank questionBank) {
        questionBank.setUpdateBy(getUsername());
        int result = questionBankMapper.updateQuestionBank(questionBank);

        // 发布题目更新事件到ES
        if (result > 0) {
            try {
                // 获取完整的题目信息
                QhQuestionBank fullQuestionBank = questionBankMapper.selectQuestionBankById(questionBank.getId());
                if (fullQuestionBank != null) {
                    // 获取试卷信息
                    QhExamPaper qhExamPaper = null;
                    if (StringUtils.hasText(fullQuestionBank.getSourcePaper())) {
                        qhExamPaper = qhExamPaperMapper.selectQhExamPaperById(fullQuestionBank.getSourcePaper());
                    }
                    // 转换为ES文档并发布更新事件
                    QuestionDocument questionDocument = convertToQuestionDocument(fullQuestionBank, qhExamPaper, null);
                    questionDocument.setUpdateTime(new Date());
                    questionEventPublisher.publishUpdateEvent(questionDocument);
                }
            } catch (Exception e) {
                log.error("发布题目更新事件失败，ID：{}，错误信息：{}", questionBank.getId(), e.getMessage());
                throw new QhException("发布题目更新事件失败: {}", e.getMessage());
            }
        }
        return result;
    }

    /**
     * 通过题目ID删除题目
     *
     * @param id 题目ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteQuestionBankById(String id) {
        QhQuestionBank qhQuestionBank = new QhQuestionBank();
        qhQuestionBank.setId(id);
        qhQuestionBank.setUpdateBy(getUsername());
        // 删除与节点的关联关系
        QhKnowledgeQuestion qhKnowledgeQuestion = new QhKnowledgeQuestion();
        qhKnowledgeQuestion.setQuestionBankId(id);
        knowledgeQuestionMapper.deleteKnowledgeQuestion(qhKnowledgeQuestion);

        // 发布题目删除事件到ES
        try {
            questionEventPublisher.publishDeleteEvent(id);
        } catch (Exception e) {
            log.error("发布题目删除事件失败，ID：{}，错误信息：{}", id, e.getMessage());
            throw new QhException("发布题目删除事件失败: {}", e.getMessage());
        }

        return questionBankMapper.deleteQuestionBankById(qhQuestionBank);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addQuestionBank(PaperQuestionAnalysisForm form) {
        PaperAnalysisForm paperAnalysisForm = form.getPaperAnalysisForm();
        List<QuestionAnalysisForm> questionAnalysisFormList = form.getQuestionAnalysisFormList();
        String userId = String.valueOf(SecurityUtils.getUserId());

        // 试卷处理
        QhExamPaper qhExamPaper = Optional.ofNullable(qhExamPaperMapper.selectQhExamPaperByPaperName(paperAnalysisForm.getPaperName(), userId))
                .map(existingPaper -> {
                    // 如果试卷已经存在，则更新已有试卷
                    setExamPaperCommonFields(existingPaper, paperAnalysisForm, questionAnalysisFormList.size(), userId);
                    qhExamPaperMapper.updateQhExamPaper(existingPaper);
                    return existingPaper;
                })
                .orElseGet(() -> {
                    // 如果试卷不存在，则创建新试卷
                    QhExamPaper newPaper = new QhExamPaper();
                    newPaper.setId(IdUtil.fastSimpleUUID());
                    setExamPaperCommonFields(newPaper, paperAnalysisForm, questionAnalysisFormList.size(), userId);
                    qhExamPaperMapper.insertQhExamPaper(newPaper);
                    return newPaper;
                });
        // QhExamPaper qhExamPaper = Optional.ofNullable(qhExamPaperMapper.selectQhExamPaperByPaperName(paperAnalysisForm.getPaperName(), userId))
        //         .map(existingPaper -> {
        //             // 如果试卷已经存在，则更新已有试卷
        //             setExamPaperCommonFields(existingPaper, paperAnalysisForm, questionAnalysisFormList.size(), userId);
        //             qhExamPaperMapper.updateQhExamPaper(existingPaper);
        //             return existingPaper;
        //         })
        //         .orElseGet(() -> {
        //             // 如果试卷不存在，则创建新试卷
        //             QhExamPaper newPaper = new QhExamPaper();
        //             newPaper.setId(IdUtil.fastSimpleUUID());
        //             setExamPaperCommonFields(newPaper, paperAnalysisForm, questionAnalysisFormList.size(), userId);
        //             qhExamPaperMapper.insertQhExamPaper(newPaper);
        //             return newPaper;
        //         });

        // 覆盖掉redis旧的解析结果
        QhProcessPaperDTO paper = new QhProcessPaperDTO();
        List<QhProcessPaperDetailDTO> details = new ArrayList<>();
        paper.setId(qhExamPaper.getId());
        paper.setPaperName(paperAnalysisForm.getPaperName());
        paper.setPaperType(paperAnalysisForm.getPaperType());
        paper.setPyear(paperAnalysisForm.getPyear());
        paper.setRegion(String.join(",", paperAnalysisForm.getRegion()));
        paper.setGrade(paperAnalysisForm.getGrade());
        paper.setSubject(paperAnalysisForm.getSubject());

        // 试题处理
        for (QuestionAnalysisForm questionAnalysisForm : questionAnalysisFormList) {
            QhQuestionBank qhQuestionBank = new QhQuestionBank();
            qhQuestionBank.setId(IdUtils.fastSimpleUUID());
            qhQuestionBank.setKnowledgeTreeIds(questionAnalysisForm.getKnowledgeTreeIds());
            qhQuestionBank.setQuestionType(questionAnalysisForm.getQuestionType());
            qhQuestionBank.setContext(questionAnalysisForm.getContext());
            qhQuestionBank.setOcrText(questionAnalysisForm.getOcrText());
            qhQuestionBank.setQuestionAnalyze(questionAnalysisForm.getQuestionAnalyze());
            qhQuestionBank.setQuestionAnswer(questionAnalysisForm.getQuestionAnswer());
            qhQuestionBank.setDifficulty(questionAnalysisForm.getDifficulty());
            qhQuestionBank.setSourcePaper(qhExamPaper.getId());
            qhQuestionBank.setTag(questionAnalysisForm.getTag());
            qhQuestionBank.setCreateBy(String.valueOf(SecurityUtils.getUserId()));
            qhQuestionBank.setDelFlag("0");
            questionBankMapper.insertQuestionBank(qhQuestionBank);

            // 插入中间表
            questionAnalysisForm.getKnowledgeTreeIds().forEach(knowledgeTreeId -> {
                QhKnowledgeQuestion qhKnowledgeQuestion = new QhKnowledgeQuestion();
                qhKnowledgeQuestion.setId(IdUtil.fastSimpleUUID());
                qhKnowledgeQuestion.setKnowledgeTreeId(knowledgeTreeId);
                qhKnowledgeQuestion.setQuestionBankId(qhQuestionBank.getId());
                qhKnowledgeQuestion.setCreateBy(SecurityUtils.getUsername());
                qhKnowledgeQuestion.setDelFlag("0");
                knowledgeQuestionMapper.insertKnowledgeQuestion(qhKnowledgeQuestion);
            });

            // 发布题目新增事件到ES
            try {
                QuestionDocument questionDocument = convertToQuestionDocument(qhQuestionBank, qhExamPaper, questionAnalysisForm);
                questionEventPublisher.publishInsertEvent(questionDocument);
            } catch (Exception e) {
                log.error("发布题目新增事件失败，题目ID：{}，错误信息：{}", qhQuestionBank.getId(), e.getMessage());
                throw new QhException("发布题目新增事件失败: {}", e.getMessage());
            }

            // 覆盖掉redis旧的解析结果
            QhProcessPaperDetailDTO qhProcessPaperDetailDTO = new QhProcessPaperDetailDTO();
            qhProcessPaperDetailDTO.setQuestionType(questionAnalysisForm.getQuestionType());
            qhProcessPaperDetailDTO.setDifficulty(questionAnalysisForm.getDifficulty());
            qhProcessPaperDetailDTO.setScore(questionAnalysisForm.getScore());
            qhProcessPaperDetailDTO.setBankId(questionAnalysisForm.getBankId());
            qhProcessPaperDetailDTO.setKnowledgeTreeIds(questionAnalysisForm.getKnowledgeTreeIds());
            qhProcessPaperDetailDTO.setTag(questionAnalysisForm.getTag());
            qhProcessPaperDetailDTO.setOcrText(questionAnalysisForm.getOcrText());
            qhProcessPaperDetailDTO.setContext(questionAnalysisForm.getContext());
            qhProcessPaperDetailDTO.setQuestionAnalyze(questionAnalysisForm.getQuestionAnalyze());
            details.add(qhProcessPaperDetailDTO);
        }

        paper.setDetails(details);
        String statusKey = "processPaper:" + SecurityUtils.getUserId() + ":" + paperAnalysisForm.getPaperName();
        redisCache.setCacheMapValue(statusKey, "status", "finished");
        redisCache.setCacheMapValue(statusKey, "result", JSONObject.toJSONString(paper));
    }

    /**
     * 设置试卷公共属性
     */
    private void setExamPaperCommonFields(QhExamPaper paper, PaperAnalysisForm form, int questionCount, String userId) {
        paper.setPaperName(form.getPaperName());
        paper.setPaperType(form.getPaperType());
        paper.setNum(String.valueOf(questionCount));
        paper.setPaperStyle("4"); // 4表示原生试卷
        paper.setPyear(form.getPyear());
        paper.setRegion(form.getRegion());
        paper.setGrade(form.getGrade());
        paper.setSubject(form.getSubject());
        paper.setCreateBy(userId);
        paper.setDelFlag("0");
    }

    /**
     * 批量删除题目信息
     *
     * @param ids 需要删除的题目ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteQuestionBankByIds(List<String> ids) {
        int bankByIds = questionBankMapper.deleteQuestionBankByIds(ids, getUsername());
        // 发布批量删除事件到ES
        try {
            questionEventPublisher.publishBatchDeleteEvent(ids);
            log.info("批量删除事件已发布，数量：{}", ids.size());
        } catch (Exception e) {
            log.error("发布批量删除事件失败，错误信息：{}", e.getMessage());
            throw new QhException("发布批量删除事件失败: {}", e.getMessage());
        }
        return bankByIds;
    }

    @Override
    public void addQuestionBoard(String type, String id) {
        QhQuestionBank qhQuestionBank = questionBankMapper.selectQuestionBankById(id);
        if (Objects.isNull(qhQuestionBank)) {
            throw new RuntimeException("题目信息缺失，请刷新后重试！");
        }

        String key;
        if ("board".equals(type)) {
            key = QhConstants.QUESTION_BOARD + SecurityUtils.getUserId();
        } else if ("export".equals(type)) {
            key = QhConstants.QUESTION_EXPORT + SecurityUtils.getUserId();
        } else {
            throw new RuntimeException("参数异常！");
        }

        // 如果已经在试题栏中，则不要重复添加了
        Double score = stringRedisTemplate.opsForZSet().score(key, qhQuestionBank.getId());
        if (Objects.nonNull(score)) {
            throw new RuntimeException("该试题已被加入试题栏中，请勿重复添加！");
        }

        // 通过 Lua 脚本保证原子性操作
        String luaScript =
                "local key = KEYS[1]\n" +
                        "local id = ARGV[1]\n" +
                        "\n" +
                        "-- 获取当前最大分数\n" +
                        "local maxScore = 0\n" +
                        "local exists = redis.call('EXISTS', key)\n" +
                        "if exists == 1 then\n" +
                        "    local results = redis.call('ZREVRANGE', key, 0, 0, 'WITHSCORES')\n" +
                        "    if #results > 0 then\n" +
                        "        maxScore = tonumber(results[2])\n" +
                        "    end\n" +
                        "end\n" +
                        "\n" +
                        "-- 生成新分数（当前最大值 +1）\n" +
                        "local newScore = maxScore + 1\n" +
                        "redis.call('ZADD', key, newScore, id)\n" +
                        "return newScore";

        Long newScore = stringRedisTemplate.execute(
                new DefaultRedisScript<>(luaScript, Long.class),
                Collections.singletonList(key),
                id // 只需要传递 id 参数
        );
        System.out.println(newScore);
    }

    @Override
    public void removeQuestionBoard(String type, String id) {
        String key;
        if ("board".equals(type)) {
            key = QhConstants.QUESTION_BOARD + SecurityUtils.getUserId();
        } else if ("export".equals(type)) {
            key = QhConstants.QUESTION_EXPORT + SecurityUtils.getUserId();
        } else {
            throw new RuntimeException("参数异常！");
        }

        // 如果传递的id是all，则说明将试题栏中所有的试题都删除
        if ("all".equals(id)) {
            stringRedisTemplate.delete(key);
            return;
        }

        // 删除 score 参数，脚本参数只需要 id
        String luaScript = "return redis.call('ZREM', KEYS[1], ARGV[1])";
        stringRedisTemplate.execute(
                new DefaultRedisScript<>(luaScript, Long.class),
                Collections.singletonList(key),
                id // 只传 id 参数
        );
    }

    @Override
    public List<QhQuestionBank> getQuestionBoard(String type) {
        String key;
        if ("board".equals(type)) {
            key = QhConstants.QUESTION_BOARD + SecurityUtils.getUserId();
        } else if ("export".equals(type)) {
            key = QhConstants.QUESTION_EXPORT + SecurityUtils.getUserId();
        } else {
            throw new RuntimeException("参数异常！");
        }
        Set<String> questionIds = stringRedisTemplate.opsForZSet().range(key, 0, -1);
        if (CollectionUtils.isEmpty(questionIds)) {
            return Collections.emptyList();
        }
        List<QhQuestionBank> qhQuestionBankList = questionBankMapper.selectQuestionBankBatchByIdsOrderByIds(new ArrayList<>(questionIds));
        // 填充节点数据
        fillKnowledgeTreeData(qhQuestionBankList);
        return qhQuestionBankList;
    }

    @Override
    public void orderQuestionBoard(String type, List<QhQuestionBoardOrder> orderList) {
        String key;
        if ("board".equals(type)) {
            key = QhConstants.QUESTION_BOARD + SecurityUtils.getUserId();
        } else if ("export".equals(type)) {
            key = QhConstants.QUESTION_EXPORT + SecurityUtils.getUserId();
        } else {
            throw new RuntimeException("参数异常！");
        }

        // 1. 参数预处理
        Map<String, Double> updateMap = new HashMap<>();
        List<Object> currentScores = new ArrayList<>();

        // 2. 使用管道批量获取当前分数
        List<Object> pipelineResults = stringRedisTemplate.executePipelined((RedisCallback<Object>) connection -> {
            orderList.forEach(item -> {
                String id = item.getId();
                Integer orderNum = item.getOrderNum();

                // 存储待比较的orderNum
                updateMap.put(id, orderNum.doubleValue());
                // 将 ZSCORE 命令加入管道
                connection.zScore(key.getBytes(), id.getBytes());
            });
            return null;
        });

        // 3. 收集需要更新的条目
        Map<String, Double> needUpdate = new HashMap<>();
        for (int i = 0; i < orderList.size(); i++) {
            QhQuestionBoardOrder item = orderList.get(i);
            String id = item.getId();
            Double currentScore = (Double) pipelineResults.get(i);
            Double targetScore = updateMap.get(id);

            // 判断是否需要更新（处理不存在或分数不同的情况）
            if (currentScore == null || !currentScore.equals(targetScore)) {
                needUpdate.put(id, targetScore);
            }
        }

        // 4. 批量执行更新（有需要更新的情况才执行）
        if (!needUpdate.isEmpty()) {
            stringRedisTemplate.executePipelined((RedisCallback<Object>) connection -> {
                needUpdate.forEach((id, score) -> {
                    connection.zAdd(key.getBytes(), score, id.getBytes());
                });
                return null;
            });
        }
    }

    /**
     * 从ES中搜索题目
     */
    private QuestionBankVO<QhQuestionBank> searchQuestionsFromES(QhQuestionBank questionBank) {
        try {
            // 构建ES搜索条件
            QuestionSearchForm searchForm = buildSearchFormFromQuestionBank(questionBank);
            // 执行ES搜索
            Map<String, Object> searchResult = questionElasticSearchService.searchQuestions(searchForm);

            if (searchResult != null && searchResult.containsKey("list")) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> esResults = (List<Map<String, Object>>) searchResult.get("list");
                if (!CollectionUtils.isEmpty(esResults)) {
                    // 将ES搜索结果转换为QhQuestionBank对象
                    String total = searchResult.get("total").toString();
                    return convertESResultsToQuestionBanks(esResults, total);
                }
            }
        } catch (Exception e) {
            log.error("ES查询失败，将使用数据库查询，错误信息：{}", e.getMessage());
        }
        return null;
    }

    /**
     * 将QhQuestionBank查询条件转换为ES搜索表单
     */
    private QuestionSearchForm buildSearchFormFromQuestionBank(QhQuestionBank questionBank) {
        QuestionSearchBuilder builder = QuestionSearchBuilder.builder();

        // 设置题目过滤
        if (!CollectionUtils.isEmpty(questionBank.getKnowledgeTreeIds())) {
            builder.knowledgeTreeIds(questionBank.getKnowledgeTreeIds());
        }

        // 设置题目类型过滤（支持多选）
        if (StringUtils.hasText(questionBank.getQuestionType())) {
            builder.questionType(questionBank.getQuestionType());
        }
        if (!CollectionUtils.isEmpty(questionBank.getQuestionTypeList())) {
            builder.questionTypes(questionBank.getQuestionTypeList());
        }

        // 设置难度过滤（支持多选）
        if (StringUtils.hasText(questionBank.getDifficulty())) {
            builder.difficulty(questionBank.getDifficulty());
        }
        if (!CollectionUtils.isEmpty(questionBank.getDifficultyList())) {
            builder.difficulties(questionBank.getDifficultyList());
        }

        // 设置试卷类型过滤（支持多选）
        if (!CollectionUtils.isEmpty(questionBank.getPaperTypeList())) {
            builder.paperTypes(questionBank.getPaperTypeList());
        }

        // 设置关键词搜索（在题干和解析中搜索）
        if (StringUtils.hasText(questionBank.getKeyword())) {
            builder.keyword(questionBank.getKeyword());
        }

        // 设置创建者过滤
        if (StringUtils.hasText(questionBank.getCreateBy())) {
            builder.createBy(questionBank.getCreateBy());
        }

        // 设置试题标签过滤
        if (StringUtils.hasText(questionBank.getTag())) {
            builder.tags(Collections.singletonList(questionBank.getTag()));
        }

        // 设置试题年份过滤
        if (!CollectionUtils.isEmpty(questionBank.getYearList())) {
            builder.years(questionBank.getYearList());
        }

        // 设置试题地区过滤
        if (!CollectionUtils.isEmpty(questionBank.getRegionList())) {
            builder.region(String.join(",", questionBank.getRegionList()));
        }

        // 设置试题来源过滤
        if (StringUtils.hasText(questionBank.getSourcePaper())) {
            builder.paperSource(questionBank.getSourcePaper());
        }

        if (questionBank.getPageNum() == null || questionBank.getPageNum() < 1) {
            questionBank.setPageNum(1);
        }
        if (questionBank.getPageSize() == null || questionBank.getPageSize() < 1 || questionBank.getPageSize() > 100) {
            questionBank.setPageSize(10);
        }

        // 使用Builder构建，自动设置默认值
        return builder.page(questionBank.getPageNum(), questionBank.getPageSize())
                .sort("createTime", "desc").build();
    }

    /**
     * 将ES搜索结果转换为QhQuestionBank对象列表
     */
    private QuestionBankVO<QhQuestionBank> convertESResultsToQuestionBanks(List<Map<String, Object>> esResults, String total) {
        List<QhQuestionBank> questionBanks = new ArrayList<>();
        for (Map<String, Object> esResult : esResults) {
            try {
                QhQuestionBank questionBank = new QhQuestionBank();
                // 基本字段映射
                questionBank.setId(getStringValue(esResult, "id"));
                questionBank.setQuestionType(getStringValue(esResult, "questionType"));
                questionBank.setContext(getStringValue(esResult, "context"));
                questionBank.setOcrText(getStringValue(esResult, "ocrText"));
                questionBank.setQuestionAnalyze(getStringValue(esResult, "questionAnalyze"));
                questionBank.setDifficulty(getStringValue(esResult, "difficulty"));
                questionBank.setTag(getStringValue(esResult, "tag"));
                questionBank.setCreateBy(getStringValue(esResult, "createBy"));

                // ✅ 试卷相关字段映射（只映射QhQuestionBank中存在的字段）
                questionBank.setSourcePaper(getStringValue(esResult, "sourcePaper"));
                questionBank.setPaperType(getStringValue(esResult, "paperType"));
                questionBank.setRegion(getStringValue(esResult, "region"));
                questionBank.setYear(getStringValue(esResult, "year"));
                questionBank.setGradeId(getStringValue(esResult, "gradeId"));

                // 其他字段映射
                // questionBank.setCategory(getStringValue(esResult, "category"));

                // 知识点ID列表（优先使用直接关联的知识点）
                Object knowledgeTreeIds = esResult.get("knowledgeTreeIds");
                if (knowledgeTreeIds instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<String> ids = (List<String>) knowledgeTreeIds;
                    questionBank.setKnowledgeTreeIds(ids);
                } else {
                    // 如果没有直接关联的知识点，尝试从所有知识点中获取
                    Object allKnowledgeTreeIds = esResult.get("allKnowledgeTreeIds");
                    if (allKnowledgeTreeIds instanceof List) {
                        @SuppressWarnings("unchecked")
                        List<String> allIds = (List<String>) allKnowledgeTreeIds;
                        questionBank.setKnowledgeTreeIds(allIds);
                    }
                }

                // 时间字段处理
                Object createTime = esResult.get("createTime");
                if (createTime != null) {
                    // ES中的时间可能是字符串格式，需要转换
                    if (createTime instanceof String) {
                        try {
                            questionBank.setCreateTime(DateUtils.parseDate(createTime.toString()));
                        } catch (Exception e) {
                            log.warn("解析创建时间失败：{}", createTime);
                        }
                    }
                }

                questionBanks.add(questionBank);
            } catch (Exception e) {
                log.error("转换ES结果失败：{}", e.getMessage());
            }
        }
        return new QuestionBankVO<>(questionBanks, Integer.parseInt(total));
    }

    /**
     * 安全获取Map中的字符串值
     */
    private String getStringValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : null;
    }

    /**
     * 将QhQuestionBank转换为QuestionDocument用于ES存储
     */
    private QuestionDocument convertToQuestionDocument(QhQuestionBank qhQuestionBank, QhExamPaper qhExamPaper, QuestionAnalysisForm questionAnalysisForm) {
        QuestionDocument document = new QuestionDocument();

        // 题目基本信息
        document.setId(qhQuestionBank.getId());
        document.setKnowledgeTreeIds(qhQuestionBank.getKnowledgeTreeIds());
        document.setQuestionType(qhQuestionBank.getQuestionType());
        document.setQuestionTypeName(getQuestionTypeName(qhQuestionBank.getQuestionType()));
        document.setContext(qhQuestionBank.getContext());
        document.setOcrText(qhQuestionBank.getOcrText());
        document.setQuestionAnalyze(qhQuestionBank.getQuestionAnalyze());
        document.setDifficulty(qhQuestionBank.getDifficulty());
        document.setTag(qhQuestionBank.getTag());

        // 试卷基本信息
        if (qhExamPaper != null) {
            document.setPaperName(qhExamPaper.getPaperName());
            document.setPaperType(qhExamPaper.getPaperType());
            document.setRegion(qhExamPaper.getRegion());
            document.setYear(qhExamPaper.getPyear());
            document.setGradeId(qhExamPaper.getGrade());
            document.setSubject(qhExamPaper.getSubject());
            document.setSourcePaper(qhExamPaper.getId());
        }

        // 设置创建信息
        document.setCreateBy(qhQuestionBank.getCreateBy());
        document.setCreateTime(new Date());

        return document;
    }

    /**
     * 根据题目类型代码获取题目类型名称
     */
    private String getQuestionTypeName(String questionType) {
        if (questionType == null) {
            return null;
        }

        try {
            Integer typeCode = Integer.parseInt(questionType);
            return QhPaperProcessType.getName(typeCode);
        } catch (NumberFormatException e) {
            return questionType;
        }
    }
}
