<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.domino.qh.mapper.QhExamPaperMapper">

    <resultMap id="BaseResultMap" type="com.domino.common.qh.domain.QhExamPaper">
        <id property="id" column="id"/>
        <result property="paperName" column="parent_name"/>
        <result property="paperType" column="paper_type"/>
        <result property="paperStyle" column="paper_style"/>
        <result property="sourcePaper" column="source_paper"/>
        <result property="region" column="region"/>
        <result property="num" column="num"/>
        <result property="score" column="score"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="updated_by"/>
        <result property="updateTime" column="updated_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="gdelFlag" column="gdel_flag"/>
        <result property="folder" column="folder"/>
    </resultMap>

    <resultMap type="com.domino.common.qh.domain.QhExamPaper" id="QhExamPaperResult">
        <result property="id" column="id"/>
        <result property="paperName" column="paper_name"/>
        <result property="paperType" column="paper_type"/>
        <result property="paperStyle" column="paper_style"/>
        <result property="region" column="region"/>
        <result property="sourcePaper" column="sourcePaper"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="gdelFlag" column="gdel_flag"/>
        <result property="folder" column="folder"/>
    </resultMap>

    <sql id="selectQhExamPaperVo">
        select id,
               paper_name,
               paper_type,
               paper_style,
               region,
               num,
               score,
               pyear,
               sourcePaper,
               create_by,
               create_time,
               update_by,
               update_time,
               del_flag,
               gdel_flag,
               folder
        from qh_exam_paper
    </sql>

    <sql id="selectKnowledgeQuestionVo">
        select id, knowledge_tree_id, question_bank_id
        from qh_knowledge_question
    </sql>

    <select id="selectQhExamPaperList" parameterType="com.domino.common.qh.domain.QhExamPaper"
            resultMap="QhExamPaperResult">
        <include refid="selectQhExamPaperVo"/>
        <where>
            <if test="flag == null or flag == ''">and del_flag = '0'</if>
            <if test="flag != null and flag != '' and flag == 'ZJ'">and gdel_flag = '0' and paper_style <![CDATA[ <> ]]> '4'</if>
            <if test="flag != null and flag != '' and flag == 'SJ'">and del_flag = '0' and paper_style = '4'</if>
            <if test="id != null  and id != ''">and id = #{id}</if>
            <if test="paperName != null  and paperName != ''">and paper_name like concat('%', #{paperName}, '%')</if>
            <if test="paperType != null  and paperType != ''">and paper_type = #{paperType}</if>
            <if test="paperStyle != null  and paperStyle != ''">and paper_style = #{paperStyle}</if>
            <if test="region != null  and region != ''">and region = #{region}</if>
            <if test="pyear != null  and pyear != ''">and pyear = #{pyear}</if>
            <if test="folder != null  and folder != ''">and folder = #{folder}</if>
            <if test="sourcePaper != null  and sourcePaper != ''">and sourcePaper = #{sourcePaper}</if>
        </where>

        order by create_time desc
    </select>

    <select id="selectBatchQhExamPaperList" resultMap="QhExamPaperResult">
        <include refid="selectQhExamPaperVo"/>
        <where>
            id IN
            <foreach item="item" collection="idList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="selectQhExamPaperById" parameterType="String" resultMap="QhExamPaperResult">
        <include refid="selectQhExamPaperVo"/>
        where id = #{id}
    </select>

    <select id="selectQhExamPaperByPaperName" resultMap="QhExamPaperResult">
        <include refid="selectQhExamPaperVo"/>
        where paper_name = #{paperName} and create_by = #{userId} and del_flag = '0'
    </select>

    <select id="selectBankIds" parameterType="String" resultType="java.lang.String">
        select question_bank_id from qh_exam_paper_question
        where paper_id = #{id}
    </select>

    <insert id="insertQhExamPaper" parameterType="com.domino.common.qh.domain.QhExamPaper">
        insert into qh_exam_paper
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="paperName != null">paper_name,</if>
            <if test="paperType != null">paper_type,</if>
            <if test="paperStyle != null">paper_style,</if>
            <if test="region != null">region,</if>
            <if test="num != null">num,</if>
            <if test="score != null">score,</if>
            <if test="pyear != null">pyear,</if>
            <if test="sourcePaper != null">sourcePaper,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="gdelFlag != null">gdel_flag,</if>
            <if test="folder != null">folder,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="paperName != null">#{paperName},</if>
            <if test="paperType != null">#{paperType},</if>
            <if test="paperStyle != null">#{paperStyle},</if>
            <if test="region != null">#{region},</if>
            <if test="num != null">#{num},</if>
            <if test="score != null">#{score},</if>
            <if test="pyear != null">#{pyear},</if>
            <if test="sourcePaper != null">#{sourcePaper},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="gdelFlag != null">#{gdelFlag},</if>
            <if test="folder != null">#{folder},</if>
        </trim>
    </insert>

    <update id="updateQhExamPaper" parameterType="com.domino.common.qh.domain.QhExamPaper">
        update qh_exam_paper
        <trim prefix="SET" suffixOverrides=",">
            <if test="paperName != null">paper_name = #{paperName},</if>
            <if test="paperType != null">paper_type = #{paperType},</if>
            <if test="paperStyle != null">paper_style = #{paperStyle},</if>
            <if test="region != null">region = #{region},</if>
            <if test="pyear != null">pyear = #{pyear},</if>
            <if test="num != null">num = #{num},</if>
            <if test="score != null">score = #{score},</if>
            <if test="sourcePaper != null">sourcePaper = #{sourcePaper},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="gdelFlag != null">gdel_flag = #{gdelFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteQhExamPaperById" parameterType="String">
        update qh_exam_paper
        set del_flag = '1'
        where id = #{id}
    </delete>

    <delete id="deleteQhGExamPaperById" parameterType="String">
        update qh_exam_paper
        set gdel_flag = '1'
        where id = #{id}
    </delete>

    <delete id="deleteQhExamPaperQuestionById" parameterType="String">
        delete
        from qh_exam_paper_question
        where paper_id = #{id}
    </delete>

    <delete id="deleteQhGExamPaperByIds" parameterType="String">
        update qh_exam_paper set gdel_flag = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteQhExamPaperByIds" parameterType="String">
        update qh_exam_paper set del_flag = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteQhExamPaperQuestionByIds" parameterType="String">
        delete from qh_exam_paper_question where paper_id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsert">
        INSERT INTO qh_exam_paper_question (id, paper_id, question_bank_id)
        VALUES
        <foreach collection="questionList" item="item" separator=",">
            (#{item.id},
            #{item.paperId},
            #{item.questionBankId})
        </foreach>
    </insert>

    <select id="selectKnowledgeQuestionList" resultType="java.lang.String">
        select distinct knowledge_tree_id from qh_knowledge_question
        WHERE question_bank_id IN
        <foreach collection="questionBankIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectCount" parameterType="Map" resultType="Integer">
        SELECT COUNT(*)
        FROM qh_exam_paper
        WHERE del_flag = '0'
        <if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
            AND date_format(create_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
        </if>
        <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
            AND date_format(create_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
        </if>
    </select>
</mapper>
