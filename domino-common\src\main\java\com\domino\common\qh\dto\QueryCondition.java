package com.domino.common.qh.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class QueryCondition implements Serializable {
    private static final long serialVersionUID = -1L;

    private String questionType;
    private String difficulty; // 难度
    private String score; // 分值
    private Double minDifficulty;
    private Double maxDifficulty;
    private List<String> years;
    private List<String> grades;
    private List<String> regions;
    private List<String> types;
    private Double minScore;
    private Double maxScore;
    private Double avgDifficulty;
    private List<String> knowledgeTreeIds;

}